{"version": "0.2.0", "configurations": [{"name": "Stripe: Webhooks listen", "type": "stripe", "request": "launch", "command": "listen", "forwardTo": "http://localhost:4242/webhook", "events": ["checkout.session.completed", "checkout.session.async_payment_failed"], "skipVerify": true}, {"type": "node", "request": "launch", "name": "Launch NestJS", "program": "${workspaceFolder}/src/main.ts", "preLaunchTask": "tsc: build - tsconfig.json", "outFiles": ["${workspaceFolder}/dist/**/*.js"]}], "compounds": [{"name": "Launch: Stripe + API", "configurations": ["Launch NestJS", "Stripe: Webhooks listen"]}]}