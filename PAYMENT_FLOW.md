Based on the codebase, here's how payments are processed and saved in the database for guest purchases:

Guest Payment Flow
The system handles guest purchases for four types of services:

Mentor services
Packages
Immigration services
Training services
For each service type, the flow is similar:

A guest initiates a purchase through a non-authenticated endpoint
Stripe checkout session is created
Payment is processed through Stripe
Webhook handles the payment completion
Guest purchase data is saved to the appropriate database table
Database Storage
Guest purchases are stored in dedicated tables:

Database Storage
Guest purchases are stored in dedicated tables:

CREATE TABLE "guest_package" (
    "id" TEXT NOT NULL,
    "amount" INTEGER NOT NULL,
    "status" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "mobile_no" TEXT NOT NULL,
    "progress" "Status" NOT NULL DEFAULT 'Pending',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "packageId" TEXT NOT NULL,
    CONSTRAINT "guest_package_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "guest_immigration_service" (
    /* Similar structure */
);

CREATE TABLE "guest_training" (
    /* Similar structure */
);

Payment Processing Flow
Guest initiates purchase: Through endpoints like /payment/guest-service, /payment/guest-package, etc.
Stripe session creation: The payment service creates a Stripe checkout session with metadata:

async guest_service(dto: UserMentorServiceDto) {
  const service = await this.prisma.service.findUnique({
    where: { id: dto.serviceId },
  });
  const session = await this.stripe.checkout.sessions.create({
    line_items: [
      {
        price_data: {
          currency: 'eur',
          product_data: {
            name: service.name,
            description: service.description,
          },
          unit_amount: service.price * 100,
        },
        quantity: 1,
      },
    ],
    metadata: {
      serviceId: service.id,
      amount: service.price,
      name: dto.name,
      email: dto.email,
      mobile_no: dto.mobile_no,
      type: 'guest-service',
    },
    mode: 'payment',
    success_url: service.meeting_link,
    cancel_url: process.env.CANCELED_URL,
  });
  
  return { status: 'OK', url: session.url };
}

Webhook handling: After payment completion, the webhook endpoint processes the event:

async webhook(req) {
  // Verify and process the webhook event
  
  switch (event.type) {
    case 'checkout.session.completed':
      const session = event.data.object;
      if (session.metadata.type === 'guest-service') {
        await this.guestService({
          amount: Number(session.metadata.amount),
          serviceId: session.metadata.serviceId,
          status: session.payment_status,
          email: session.metadata.email,
          mobile_no: session.metadata.mobile_no,
          name: session.metadata.name,
          userId: '',
        });
      }
      if (session.metadata.type === 'guest-package') {
        await this.guestPackage({
          // Similar structure
        });
      }
      // Similar handlers for immigration and training
      break;
    // Handle other event types
  }
}

Database storage: The payment data is saved to the appropriate guest table:

async guestService(data: UserServiceDto) {
  const { userId, ...result } = data;
  const service = await this.prisma.guest_mentor_service.create({
    data: { ...result },
    include: {
      mentor_services: {
        include: { mentor: true }
      }
    }
  });
  
  // Send confirmation emails
  if (service) {
    await this.mailer.sendEmail({
      // Email to guest
    });
    await this.mailer.sendEmail({
      // Notification to admin
    });
  }
  
  return service;
}

The system also includes functionality to retrieve guest purchase records through the  GuestService with endpoints for each service type.

Based on the codebase, there are two main payment types:

1. Authenticated User Purchases
For registered users who are logged in, the system processes payments with user authentication:

@UseGuards(JwtGuard)
@ApiBearerAuth()
@Post('/mentor-service')
@ApiOperation({
  summary: '(User only)',
  description: 'This API is restricted to users and requires a Bearer token for authentication.',
})
async create(
  @GetUser() user: IJWTPayload,
  @Body() dto: UserMentorServiceDto,
) {
  return await this.payment.mentor_service(user, dto);
}

These purchases are stored in user-specific tables:

user_mentor_service - For mentor service purchases
 user_package - For package purchases
user_immigration_service - For immigration service purchases
 user_training - For training program purchases

 2. Guest Purchases
As we discussed earlier, these are purchases made without authentication:
@Post('/guest-service')
@ApiOperation({
  summary: '(Guest User only)',
})
async guestService(@Body() dto: UserMentorServiceDto) {
  return await this.payment.guest_service(dto);
}

Payment Processing Flow Comparison
Both payment types follow a similar flow:

Payment Initiation:
Authenticated: Requires JWT token
Guest: No authentication required
Stripe Session Creation:
Authenticated: Includes userId in metadata
Guest: Includes guest details (name, email, mobile) in metadata
Webhook Processing:
Both use the same webhook endpoint
Different handlers based on type in metadata
Database Storage:
Authenticated: Stored in user-specific tables with user reference
Guest: Stored in guest-specific tables without user reference
Email Notifications:
Both send confirmation emails to the purchaser
Both send notification emails to admin
The main difference is that authenticated purchases maintain a relationship to the user account, while guest purchases only store the provided contact information without any user account association.



Recommendations
Add proper error handling and logging in the webhook handler
Use database transactions for data consistency
Implement idempotency to prevent duplicate processing
Add monitoring for webhook failures
Ensure the webhook endpoint is properly configured in Stripe
Verify that the STRIPE_WEBHOOK_SECRET is correctly set
Add retry logic for failed database operations
These improvements would help ensure that payment data is reliably saved to the database after successful payments.

### Data are not saved to db on success payment is their codes that avoids stripe to not saved 


Is 8 Tables Necessary?
Arguments FOR the current structure:

Clear separation of concerns between authenticated and guest purchases
Type safety - each service type has its own dedicated table
Easier querying - no need for complex WHERE clauses to filter by service type
Different data requirements - guest tables store contact info (name, email, mobile) while user tables reference user accounts
Simplified reporting - easy to generate reports per service type
Future extensibility - each service type can evolve independently
Arguments AGAINST (potential consolidation):

Code duplication - similar structure across all tables
Maintenance overhead - changes need to be applied to multiple tables
Schema complexity - more tables to manage

CREATE TABLE "payment" (
    "id" TEXT NOT NULL,
    "amount" INTEGER NOT NULL,
    "status" TEXT NOT NULL,
    "payment_type" TEXT NOT NULL, -- 'user' or 'guest'
    "service_type" TEXT NOT NULL, -- 'mentor', 'package', 'immigration', 'training'
    "userId" TEXT NULL,
    "serviceId" TEXT NULL,
    "packageId" TEXT NULL,
    "immigration_serviceId" TEXT NULL,
    "trainingId" TEXT NULL,
    -- Guest fields
    "guest_name" TEXT NULL,
    "guest_email" TEXT NULL,
    "guest_mobile" TEXT NULL,
    "progress" "Status" NOT NULL DEFAULT 'Pending',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL
);