# Payment Table Consolidation Migration Plan

## Overview

This document outlines the migration strategy to consolidate the current 8 payment tables into a single unified payment table. This migration will improve maintainability, reduce code duplication, and simplify the payment system architecture.

## Current State Analysis

### Existing Tables (8 Tables)

#### User Payment Tables (4 tables)
1. **user_mentor_service** - Authenticated mentor service purchases
2. **user_package** - Authenticated package purchases  
3. **user_immigration_service** - Authenticated immigration service purchases
4. **user_training** - Authenticated training purchases

#### Guest Payment Tables (4 tables)
1. **guest_mentor_service** - Guest mentor service purchases
2. **guest_package** - Guest package purchases
3. **guest_immigration_service** - Guest immigration service purchases
4. **guest_training** - Guest training purchases

### Common Fields Across All Tables
```sql
id              TEXT PRIMARY KEY
amount          INTEGER NOT NULL
status          TEXT NOT NULL
progress        Status DEFAULT 'Pending'
createdAt       TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP
updatedAt       TIMESTAMP(3)
```

### User-Specific Fields
```sql
userId          TEXT (Foreign Key to user table)
```

### Guest-Specific Fields
```sql
name            TEXT NOT NULL
email           TEXT NOT NULL
mobile_no       TEXT NOT NULL
```

### Service-Specific Foreign Keys
```sql
serviceId               TEXT (mentor services)
packageId               TEXT (packages)
immigration_serviceId   TEXT (immigration services)
trainingId              TEXT (training)
```

## Target State Design

### Unified Payment Table Schema

```sql
CREATE TABLE "payment" (
    "id" TEXT NOT NULL,
    "amount" INTEGER NOT NULL,
    "status" TEXT NOT NULL,
    "payment_type" TEXT NOT NULL, -- 'user' or 'guest'
    "service_type" TEXT NOT NULL, -- 'mentor', 'package', 'immigration', 'training'
    "progress" "Status" NOT NULL DEFAULT 'Pending',
    
    -- User reference (nullable for guest payments)
    "userId" TEXT NULL,
    
    -- Service references (only one will be populated per record)
    "serviceId" TEXT NULL,
    "packageId" TEXT NULL,
    "immigration_serviceId" TEXT NULL,
    "trainingId" TEXT NULL,
    
    -- Guest contact information (nullable for user payments)
    "guest_name" TEXT NULL,
    "guest_email" TEXT NULL,
    "guest_mobile" TEXT NULL,
    
    -- Stripe payment information
    "stripe_session_id" TEXT NULL,
    "stripe_payment_intent_id" TEXT NULL,
    
    -- Timestamps
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    
    CONSTRAINT "payment_pkey" PRIMARY KEY ("id")
);

-- Foreign Key Constraints
ALTER TABLE "payment" ADD CONSTRAINT "payment_userId_fkey" 
    FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "payment" ADD CONSTRAINT "payment_serviceId_fkey" 
    FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "payment" ADD CONSTRAINT "payment_packageId_fkey" 
    FOREIGN KEY ("packageId") REFERENCES "packages"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "payment" ADD CONSTRAINT "payment_immigration_serviceId_fkey" 
    FOREIGN KEY ("immigration_serviceId") REFERENCES "immigration_service"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "payment" ADD CONSTRAINT "payment_trainingId_fkey" 
    FOREIGN KEY ("trainingId") REFERENCES "training"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Indexes for performance
CREATE INDEX "payment_userId_idx" ON "payment"("userId");
CREATE INDEX "payment_service_type_idx" ON "payment"("service_type");
CREATE INDEX "payment_payment_type_idx" ON "payment"("payment_type");
CREATE INDEX "payment_status_idx" ON "payment"("status");
CREATE INDEX "payment_createdAt_idx" ON "payment"("createdAt");

-- Check constraints to ensure data integrity
ALTER TABLE "payment" ADD CONSTRAINT "payment_service_reference_check" 
    CHECK (
        (service_type = 'mentor' AND serviceId IS NOT NULL AND packageId IS NULL AND immigration_serviceId IS NULL AND trainingId IS NULL) OR
        (service_type = 'package' AND packageId IS NOT NULL AND serviceId IS NULL AND immigration_serviceId IS NULL AND trainingId IS NULL) OR
        (service_type = 'immigration' AND immigration_serviceId IS NOT NULL AND serviceId IS NULL AND packageId IS NULL AND trainingId IS NULL) OR
        (service_type = 'training' AND trainingId IS NOT NULL AND serviceId IS NULL AND packageId IS NULL AND immigration_serviceId IS NULL)
    );

ALTER TABLE "payment" ADD CONSTRAINT "payment_type_check" 
    CHECK (
        (payment_type = 'user' AND userId IS NOT NULL AND guest_name IS NULL AND guest_email IS NULL AND guest_mobile IS NULL) OR
        (payment_type = 'guest' AND userId IS NULL AND guest_name IS NOT NULL AND guest_email IS NOT NULL AND guest_mobile IS NOT NULL)
    );
```

## Migration Strategy

### Phase 1: Preparation (Zero Downtime)

#### 1.1 Create New Payment Table
```sql
-- Create the new unified payment table alongside existing tables
-- This allows for gradual migration without downtime
```

#### 1.2 Update Prisma Schema
```prisma
model payment {
  id                     String               @id @default(cuid())
  amount                 Int
  status                 String
  payment_type           String               // 'user' or 'guest'
  service_type           String               // 'mentor', 'package', 'immigration', 'training'
  progress               Status               @default(Pending)
  
  // User reference
  user                   user?                @relation(fields: [userId], references: [id], onDelete: SetNull)
  userId                 String?
  
  // Service references
  service                service?             @relation(fields: [serviceId], references: [id], onDelete: SetNull)
  serviceId              String?
  package                packages?            @relation(fields: [packageId], references: [id], onDelete: SetNull)
  packageId              String?
  immigration_service    immigration_service? @relation(fields: [immigration_serviceId], references: [id], onDelete: SetNull)
  immigration_serviceId  String?
  training               training?            @relation(fields: [trainingId], references: [id], onDelete: SetNull)
  trainingId             String?
  
  // Guest information
  guest_name             String?
  guest_email            String?
  guest_mobile           String?
  
  // Stripe information
  stripe_session_id      String?
  stripe_payment_intent_id String?
  
  createdAt              DateTime             @default(now())
  updatedAt              DateTime             @updatedAt
  
  @@index([userId])
  @@index([service_type])
  @@index([payment_type])
  @@index([status])
  @@index([createdAt])
}
```

#### 1.3 Create Migration Scripts
```typescript
// scripts/migrate-payments.ts
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function migratePayments() {
  console.log('Starting payment migration...');
  
  // Migrate user_mentor_service
  await migrateUserMentorServices();
  
  // Migrate guest_mentor_service
  await migrateGuestMentorServices();
  
  // Migrate user_package
  await migrateUserPackages();
  
  // Migrate guest_package
  await migrateGuestPackages();
  
  // Migrate user_immigration_service
  await migrateUserImmigrationServices();
  
  // Migrate guest_immigration_service
  await migrateGuestImmigrationServices();
  
  // Migrate user_training
  await migrateUserTraining();
  
  // Migrate guest_training
  await migrateGuestTraining();
  
  console.log('Payment migration completed!');
}

async function migrateUserMentorServices() {
  const records = await prisma.user_mentor_service.findMany();
  
  for (const record of records) {
    await prisma.payment.create({
      data: {
        id: record.id,
        amount: record.amount,
        status: record.status,
        payment_type: 'user',
        service_type: 'mentor',
        progress: record.progress,
        userId: record.userId,
        serviceId: record.serviceId,
        createdAt: record.createdAt,
        updatedAt: record.updatedAt,
      },
    });
  }
  
  console.log(`Migrated ${records.length} user mentor service records`);
}

// Similar functions for other tables...
```

### Phase 2: Dual Write Implementation

#### 2.1 Update Payment Service
```typescript
// Update payment service to write to both old and new tables
async user_service(data: UserServiceDto) {
  // Write to old table (existing functionality)
  const oldRecord = await this.prisma.user_mentor_service.create({
    data,
    include: {
      mentor_services: {
        include: { mentor: true }
      }
    }
  });

  // Write to new unified table
  await this.prisma.payment.create({
    data: {
      amount: data.amount,
      status: data.status,
      payment_type: 'user',
      service_type: 'mentor',
      userId: data.userId,
      serviceId: data.serviceId,
    },
  });

  return oldRecord;
}
```

#### 2.2 Data Validation
```typescript
// scripts/validate-migration.ts
async function validateMigration() {
  const oldCount = await countOldTables();
  const newCount = await prisma.payment.count();
  
  if (oldCount !== newCount) {
    throw new Error(`Migration validation failed: ${oldCount} old records vs ${newCount} new records`);
  }
  
  console.log('Migration validation passed!');
}
```

### Phase 3: Switch to New Table (Planned Downtime)

#### 3.1 Update All Service Methods
```typescript
// Replace all old table operations with new unified table operations
async user_service(data: UserServiceDto) {
  const service = await this.prisma.payment.create({
    data: {
      amount: data.amount,
      status: data.status,
      payment_type: 'user',
      service_type: 'mentor',
      userId: data.userId,
      serviceId: data.serviceId,
    },
    include: {
      service: {
        include: { mentor: true }
      }
    }
  });

  // Send emails and notifications...
  return service;
}
```

#### 3.2 Update Webhook Handler
```typescript
async webhook(req: any) {
  // ... existing webhook logic ...
  
  switch (event.type) {
    case 'checkout.session.completed':
      const session = event.data.object;
      
      // Unified payment creation
      await this.createUnifiedPayment(session);
      break;
  }
}

private async createUnifiedPayment(session: any) {
  const paymentData = {
    amount: Number(session.metadata.amount),
    status: session.payment_status,
    stripe_session_id: session.id,
    stripe_payment_intent_id: session.payment_intent,
  };

  if (session.metadata.type.startsWith('guest-')) {
    // Guest payment
    await this.prisma.payment.create({
      data: {
        ...paymentData,
        payment_type: 'guest',
        service_type: this.getServiceType(session.metadata.type),
        guest_name: session.metadata.name,
        guest_email: session.metadata.email,
        guest_mobile: session.metadata.mobile_no,
        ...this.getServiceReference(session.metadata),
      },
    });
  } else {
    // User payment
    await this.prisma.payment.create({
      data: {
        ...paymentData,
        payment_type: 'user',
        service_type: this.getServiceType(session.metadata.type),
        userId: session.metadata.userId,
        ...this.getServiceReference(session.metadata),
      },
    });
  }
}
```

### Phase 4: Cleanup (Post-Migration)

#### 4.1 Remove Old Tables
```sql
-- After confirming everything works correctly
DROP TABLE "user_mentor_service";
DROP TABLE "guest_mentor_service";
DROP TABLE "user_package";
DROP TABLE "guest_package";
DROP TABLE "user_immigration_service";
DROP TABLE "guest_immigration_service";
DROP TABLE "user_training";
DROP TABLE "guest_training";
```

#### 4.2 Update Related Models
```prisma
// Remove old relations from user model
model user {
  // Remove these lines:
  // services             user_mentor_service[]
  // packages             user_package[]
  // immigration_services user_immigration_service[]
  // training             user_training[]
  
  // Add new relation:
  payments             payment[]
}
```

## Production Impact Analysis

### Benefits
1. **Reduced Complexity**: Single table instead of 8 tables
2. **Easier Maintenance**: One set of CRUD operations
3. **Better Performance**: Fewer joins, single index strategy
4. **Simplified Reporting**: All payment data in one place
5. **Reduced Code Duplication**: Single payment service logic

### Risks and Mitigation

#### Risk 1: Data Loss During Migration
**Mitigation**: 
- Comprehensive backup before migration
- Dual-write approach during transition
- Extensive validation scripts
- Rollback plan with data restoration

#### Risk 2: Application Downtime
**Mitigation**:
- Phased migration approach
- Minimal downtime window (Phase 3 only)
- Blue-green deployment strategy
- Feature flags for gradual rollout

#### Risk 3: Performance Impact
**Mitigation**:
- Proper indexing strategy
- Query optimization
- Performance testing before production
- Monitoring during and after migration

#### Risk 4: Foreign Key Constraint Issues
**Mitigation**:
- Careful constraint design
- Data integrity validation
- Gradual constraint application
- Comprehensive testing

## Timeline Estimate

### Development Phase (2-3 weeks)
- Week 1: Schema design and migration scripts
- Week 2: Service layer updates and testing
- Week 3: Integration testing and validation

### Testing Phase (1-2 weeks)
- Staging environment migration
- Performance testing
- User acceptance testing
- Security validation

### Production Migration (1 day)
- Phase 1: Create new table (0 downtime)
- Phase 2: Enable dual writes (0 downtime)
- Phase 3: Switch to new table (2-4 hours downtime)
- Phase 4: Cleanup (0 downtime)

## Rollback Plan

### Immediate Rollback (During Phase 3)
1. Revert application code to use old tables
2. Stop writing to new payment table
3. Validate data consistency
4. Resume normal operations

### Post-Migration Rollback
1. Recreate old tables from backup
2. Migrate data back from unified table
3. Revert application code
4. Validate system functionality

## Success Criteria

1. **Data Integrity**: 100% data preservation during migration
2. **Performance**: No degradation in payment processing speed
3. **Functionality**: All payment features work as expected
4. **Monitoring**: Comprehensive logging and alerting in place
5. **Documentation**: Updated API documentation and developer guides

## Monitoring and Alerts

### Key Metrics to Monitor
- Payment processing success rate
- Database query performance
- Error rates in payment endpoints
- Webhook processing latency
- Data consistency between old and new systems (during dual-write phase)

### Alert Thresholds
- Payment failure rate > 1%
- Database query time > 500ms
- Webhook processing time > 5 seconds
- Data inconsistency detected

This migration plan ensures a safe, gradual transition from the current 8-table structure to a unified payment table while minimizing risks and maintaining system reliability.
