# Payment Table Consolidation Migration Plan

## Overview

This document outlines the migration strategy to consolidate the current 8 payment tables into a single unified payment table. This migration will improve maintainability, reduce code duplication, and simplify the payment system architecture.

## Current State Analysis

### Existing Tables (8 Tables)

#### User Payment Tables (4 tables)
1. **user_mentor_service** - Authenticated mentor service purchases
2. **user_package** - Authenticated package purchases
3. **user_immigration_service** - Authenticated immigration service purchases
4. **user_training** - Authenticated training purchases

#### Guest Payment Tables (4 tables)
1. **guest_mentor_service** - Guest mentor service purchases
2. **guest_package** - Guest package purchases
3. **guest_immigration_service** - Guest immigration service purchases
4. **guest_training** - Guest training purchases

### Common Fields Across All Tables
```sql
id              TEXT PRIMARY KEY
amount          INTEGER NOT NULL
status          TEXT NOT NULL
progress        Status DEFAULT 'Pending'
createdAt       TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP
updatedAt       TIMESTAMP(3)
```

### User-Specific Fields
```sql
userId          TEXT (Foreign Key to user table)
```

### Guest-Specific Fields
```sql
name            TEXT NOT NULL
email           TEXT NOT NULL
mobile_no       TEXT NOT NULL
```

### Service-Specific Foreign Keys
```sql
serviceId               TEXT (mentor services)
packageId               TEXT (packages)
immigration_serviceId   TEXT (immigration services)
trainingId              TEXT (training)
```

## Target State Design

### Unified Payment Table Schema

```sql
CREATE TABLE "payment" (
    "id" TEXT NOT NULL,
    "amount" INTEGER NOT NULL,
    "status" TEXT NOT NULL,
    "payment_type" TEXT NOT NULL, -- 'user' or 'guest'
    "service_type" TEXT NOT NULL, -- 'mentor', 'package', 'immigration', 'training'
    "progress" "Status" NOT NULL DEFAULT 'Pending',

    -- User reference (nullable for guest payments)
    "userId" TEXT NULL,

    -- Service references (only one will be populated per record)
    "serviceId" TEXT NULL,
    "packageId" TEXT NULL,
    "immigration_serviceId" TEXT NULL,
    "trainingId" TEXT NULL,

    -- Guest contact information (nullable for user payments)
    "guest_name" TEXT NULL,
    "guest_email" TEXT NULL,
    "guest_mobile" TEXT NULL,

    -- Stripe payment information
    "stripe_session_id" TEXT NULL,
    "stripe_payment_intent_id" TEXT NULL,

    -- Timestamps
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "payment_pkey" PRIMARY KEY ("id")
);

-- Foreign Key Constraints
ALTER TABLE "payment" ADD CONSTRAINT "payment_userId_fkey"
    FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "payment" ADD CONSTRAINT "payment_serviceId_fkey"
    FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "payment" ADD CONSTRAINT "payment_packageId_fkey"
    FOREIGN KEY ("packageId") REFERENCES "packages"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "payment" ADD CONSTRAINT "payment_immigration_serviceId_fkey"
    FOREIGN KEY ("immigration_serviceId") REFERENCES "immigration_service"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "payment" ADD CONSTRAINT "payment_trainingId_fkey"
    FOREIGN KEY ("trainingId") REFERENCES "training"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Indexes for performance
CREATE INDEX "payment_userId_idx" ON "payment"("userId");
CREATE INDEX "payment_service_type_idx" ON "payment"("service_type");
CREATE INDEX "payment_payment_type_idx" ON "payment"("payment_type");
CREATE INDEX "payment_status_idx" ON "payment"("status");
CREATE INDEX "payment_createdAt_idx" ON "payment"("createdAt");

-- Check constraints to ensure data integrity
ALTER TABLE "payment" ADD CONSTRAINT "payment_service_reference_check"
    CHECK (
        (service_type = 'mentor' AND serviceId IS NOT NULL AND packageId IS NULL AND immigration_serviceId IS NULL AND trainingId IS NULL) OR
        (service_type = 'package' AND packageId IS NOT NULL AND serviceId IS NULL AND immigration_serviceId IS NULL AND trainingId IS NULL) OR
        (service_type = 'immigration' AND immigration_serviceId IS NOT NULL AND serviceId IS NULL AND packageId IS NULL AND trainingId IS NULL) OR
        (service_type = 'training' AND trainingId IS NOT NULL AND serviceId IS NULL AND packageId IS NULL AND immigration_serviceId IS NULL)
    );

ALTER TABLE "payment" ADD CONSTRAINT "payment_type_check"
    CHECK (
        (payment_type = 'user' AND userId IS NOT NULL AND guest_name IS NULL AND guest_email IS NULL AND guest_mobile IS NULL) OR
        (payment_type = 'guest' AND userId IS NULL AND guest_name IS NOT NULL AND guest_email IS NOT NULL AND guest_mobile IS NOT NULL)
    );
```

## Migration Strategy

### Phase 1: Preparation (Zero Downtime)

#### 1.1 Create New Payment Table
```sql
-- Create the new unified payment table alongside existing tables
-- This allows for gradual migration without downtime
```

#### 1.2 Update Prisma Schema
```prisma
model payment {
  id                     String               @id @default(cuid())
  amount                 Int
  status                 String
  payment_type           String               // 'user' or 'guest'
  service_type           String               // 'mentor', 'package', 'immigration', 'training'
  progress               Status               @default(Pending)

  // User reference
  user                   user?                @relation(fields: [userId], references: [id], onDelete: SetNull)
  userId                 String?

  // Service references
  service                service?             @relation(fields: [serviceId], references: [id], onDelete: SetNull)
  serviceId              String?
  package                packages?            @relation(fields: [packageId], references: [id], onDelete: SetNull)
  packageId              String?
  immigration_service    immigration_service? @relation(fields: [immigration_serviceId], references: [id], onDelete: SetNull)
  immigration_serviceId  String?
  training               training?            @relation(fields: [trainingId], references: [id], onDelete: SetNull)
  trainingId             String?

  // Guest information
  guest_name             String?
  guest_email            String?
  guest_mobile           String?

  // Stripe information
  stripe_session_id      String?
  stripe_payment_intent_id String?

  createdAt              DateTime             @default(now())
  updatedAt              DateTime             @updatedAt

  @@index([userId])
  @@index([service_type])
  @@index([payment_type])
  @@index([status])
  @@index([createdAt])
}
```

#### 1.3 Create Migration Scripts
```typescript
// scripts/migrate-payments.ts
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function migratePayments() {
  console.log('Starting payment migration...');

  // Migrate user_mentor_service
  await migrateUserMentorServices();

  // Migrate guest_mentor_service
  await migrateGuestMentorServices();

  // Migrate user_package
  await migrateUserPackages();

  // Migrate guest_package
  await migrateGuestPackages();

  // Migrate user_immigration_service
  await migrateUserImmigrationServices();

  // Migrate guest_immigration_service
  await migrateGuestImmigrationServices();

  // Migrate user_training
  await migrateUserTraining();

  // Migrate guest_training
  await migrateGuestTraining();

  console.log('Payment migration completed!');
}

async function migrateUserMentorServices() {
  const records = await prisma.user_mentor_service.findMany();

  for (const record of records) {
    await prisma.payment.create({
      data: {
        id: record.id,
        amount: record.amount,
        status: record.status,
        payment_type: 'user',
        service_type: 'mentor',
        progress: record.progress,
        userId: record.userId,
        serviceId: record.serviceId,
        createdAt: record.createdAt,
        updatedAt: record.updatedAt,
      },
    });
  }

  console.log(`Migrated ${records.length} user mentor service records`);
}

// Similar functions for other tables...
```

### Phase 2: Dual Write Implementation

#### 2.1 Update Payment Service
```typescript
// Update payment service to write to both old and new tables
async user_service(data: UserServiceDto) {
  // Write to old table (existing functionality)
  const oldRecord = await this.prisma.user_mentor_service.create({
    data,
    include: {
      mentor_services: {
        include: { mentor: true }
      }
    }
  });

  // Write to new unified table
  await this.prisma.payment.create({
    data: {
      amount: data.amount,
      status: data.status,
      payment_type: 'user',
      service_type: 'mentor',
      userId: data.userId,
      serviceId: data.serviceId,
    },
  });

  return oldRecord;
}
```

#### 2.2 Data Validation
```typescript
// scripts/validate-migration.ts
async function validateMigration() {
  const oldCount = await countOldTables();
  const newCount = await prisma.payment.count();

  if (oldCount !== newCount) {
    throw new Error(`Migration validation failed: ${oldCount} old records vs ${newCount} new records`);
  }

  console.log('Migration validation passed!');
}
```

### Phase 3: Switch to New Table (Planned Downtime)

#### 3.1 Update All Service Methods
```typescript
// Replace all old table operations with new unified table operations
async user_service(data: UserServiceDto) {
  const service = await this.prisma.payment.create({
    data: {
      amount: data.amount,
      status: data.status,
      payment_type: 'user',
      service_type: 'mentor',
      userId: data.userId,
      serviceId: data.serviceId,
    },
    include: {
      service: {
        include: { mentor: true }
      }
    }
  });

  // Send emails and notifications...
  return service;
}
```

#### 3.2 Update Webhook Handler
```typescript
async webhook(req: any) {
  // ... existing webhook logic ...

  switch (event.type) {
    case 'checkout.session.completed':
      const session = event.data.object;

      // Unified payment creation
      await this.createUnifiedPayment(session);
      break;
  }
}

private async createUnifiedPayment(session: any) {
  const paymentData = {
    amount: Number(session.metadata.amount),
    status: session.payment_status,
    stripe_session_id: session.id,
    stripe_payment_intent_id: session.payment_intent,
  };

  if (session.metadata.type.startsWith('guest-')) {
    // Guest payment
    await this.prisma.payment.create({
      data: {
        ...paymentData,
        payment_type: 'guest',
        service_type: this.getServiceType(session.metadata.type),
        guest_name: session.metadata.name,
        guest_email: session.metadata.email,
        guest_mobile: session.metadata.mobile_no,
        ...this.getServiceReference(session.metadata),
      },
    });
  } else {
    // User payment
    await this.prisma.payment.create({
      data: {
        ...paymentData,
        payment_type: 'user',
        service_type: this.getServiceType(session.metadata.type),
        userId: session.metadata.userId,
        ...this.getServiceReference(session.metadata),
      },
    });
  }
}
```

### Phase 4: Cleanup (Post-Migration)

#### 4.1 Remove Old Tables
```sql
-- After confirming everything works correctly
DROP TABLE "user_mentor_service";
DROP TABLE "guest_mentor_service";
DROP TABLE "user_package";
DROP TABLE "guest_package";
DROP TABLE "user_immigration_service";
DROP TABLE "guest_immigration_service";
DROP TABLE "user_training";
DROP TABLE "guest_training";
```

#### 4.2 Update Related Models
```prisma
// Remove old relations from user model
model user {
  // Remove these lines:
  // services             user_mentor_service[]
  // packages             user_package[]
  // immigration_services user_immigration_service[]
  // training             user_training[]

  // Add new relation:
  payments             payment[]
}
```

## Production Impact Analysis

### Benefits
1. **Reduced Complexity**: Single table instead of 8 tables
2. **Easier Maintenance**: One set of CRUD operations
3. **Better Performance**: Fewer joins, single index strategy
4. **Simplified Reporting**: All payment data in one place
5. **Reduced Code Duplication**: Single payment service logic

### Risks and Mitigation

#### Risk 1: Data Loss During Migration
**Mitigation**:
- Comprehensive backup before migration
- Dual-write approach during transition
- Extensive validation scripts
- Rollback plan with data restoration

#### Risk 2: Application Downtime
**Mitigation**:
- Phased migration approach
- Minimal downtime window (Phase 3 only)
- Blue-green deployment strategy
- Feature flags for gradual rollout

#### Risk 3: Performance Impact
**Mitigation**:
- Proper indexing strategy
- Query optimization
- Performance testing before production
- Monitoring during and after migration

#### Risk 4: Foreign Key Constraint Issues
**Mitigation**:
- Careful constraint design
- Data integrity validation
- Gradual constraint application
- Comprehensive testing

## Timeline Estimate

### Development Phase (2-3 weeks)
- Week 1: Schema design and migration scripts
- Week 2: Service layer updates and testing
- Week 3: Integration testing and validation

### Testing Phase (1-2 weeks)
- Staging environment migration
- Performance testing
- User acceptance testing
- Security validation

### Production Migration (1 day)
- Phase 1: Create new table (0 downtime)
- Phase 2: Enable dual writes (0 downtime)
- Phase 3: Switch to new table (2-4 hours downtime)
- Phase 4: Cleanup (0 downtime)

## Rollback Plan

### Immediate Rollback (During Phase 3)
1. Revert application code to use old tables
2. Stop writing to new payment table
3. Validate data consistency
4. Resume normal operations

### Post-Migration Rollback
1. Recreate old tables from backup
2. Migrate data back from unified table
3. Revert application code
4. Validate system functionality

## Success Criteria

1. **Data Integrity**: 100% data preservation during migration
2. **Performance**: No degradation in payment processing speed
3. **Functionality**: All payment features work as expected
4. **Monitoring**: Comprehensive logging and alerting in place
5. **Documentation**: Updated API documentation and developer guides

## Monitoring and Alerts

### Key Metrics to Monitor
- Payment processing success rate
- Database query performance
- Error rates in payment endpoints
- Webhook processing latency
- Data consistency between old and new systems (during dual-write phase)

### Alert Thresholds
- Payment failure rate > 1%
- Database query time > 500ms
- Webhook processing time > 5 seconds
- Data inconsistency detected

This migration plan ensures a safe, gradual transition from the current 8-table structure to a unified payment table while minimizing risks and maintaining system reliability.

## API Endpoint Impact Analysis

### Affected Endpoints Overview

The payment migration will impact **15 API endpoints** across **5 controllers** that directly interact with the payment tables. Here's a comprehensive analysis:

### 🔴 **Critical Impact Endpoints (Payment Processing)**

#### Payment Controller (`/payment`)
These endpoints handle the core payment processing and will require significant changes:

1. **POST `/payment/mentor-service`** (User authenticated)
   - **Current**: Creates record in `user_mentor_service` table
   - **After Migration**: Creates record in unified `payment` table with `payment_type='user'` and `service_type='mentor'`
   - **Impact**: Service method rewrite, response format may change

2. **POST `/payment/guest-service`** (Guest)
   - **Current**: Creates record in `guest_mentor_service` table
   - **After Migration**: Creates record in unified `payment` table with `payment_type='guest'` and `service_type='mentor'`
   - **Impact**: Service method rewrite, response format may change

3. **POST `/payment/package`** (User authenticated)
   - **Current**: Creates record in `user_package` table
   - **After Migration**: Creates record in unified `payment` table with `payment_type='user'` and `service_type='package'`
   - **Impact**: Service method rewrite, response format may change

4. **POST `/payment/guest-package`** (Guest)
   - **Current**: Creates record in `guest_package` table
   - **After Migration**: Creates record in unified `payment` table with `payment_type='guest'` and `service_type='package'`
   - **Impact**: Service method rewrite, response format may change

5. **POST `/payment/immigration-service`** (User authenticated)
   - **Current**: Creates record in `user_immigration_service` table
   - **After Migration**: Creates record in unified `payment` table with `payment_type='user'` and `service_type='immigration'`
   - **Impact**: Service method rewrite, response format may change

6. **POST `/payment/guest-immigration`** (Guest)
   - **Current**: Creates record in `guest_immigration_service` table
   - **After Migration**: Creates record in unified `payment` table with `payment_type='guest'` and `service_type='immigration'`
   - **Impact**: Service method rewrite, response format may change

7. **POST `/payment/training`** (User authenticated)
   - **Current**: Creates record in `user_training` table
   - **After Migration**: Creates record in unified `payment` table with `payment_type='user'` and `service_type='training'`
   - **Impact**: Service method rewrite, response format may change

8. **POST `/payment/guest-training`** (Guest)
   - **Current**: Creates record in `guest_training` table
   - **After Migration**: Creates record in unified `payment` table with `payment_type='guest'` and `service_type='training'`
   - **Impact**: Service method rewrite, response format may change

9. **POST `/payment/web-hook`** (Stripe webhook)
   - **Current**: Processes webhook and creates records in 8 different tables based on payment type
   - **After Migration**: Processes webhook and creates records in unified `payment` table
   - **Impact**: Complete webhook handler rewrite

### 🟡 **High Impact Endpoints (Admin Reporting)**

#### Guest Controller (`/guest`) - Admin Only
These endpoints provide admin views of guest purchases:

10. **GET `/guest/purchase/service`** (Admin only)
    - **Current**: Queries `guest_mentor_service` table with pagination
    - **After Migration**: Query unified `payment` table with `WHERE payment_type='guest' AND service_type='mentor'`
    - **Impact**: Query rewrite, response format may change

11. **GET `/guest/purchase/package`** (Admin only)
    - **Current**: Queries `guest_package` table with pagination
    - **After Migration**: Query unified `payment` table with `WHERE payment_type='guest' AND service_type='package'`
    - **Impact**: Query rewrite, response format may change

12. **GET `/guest/purchase/immigration`** (Admin only)
    - **Current**: Queries `guest_immigration_service` table with pagination
    - **After Migration**: Query unified `payment` table with `WHERE payment_type='guest' AND service_type='immigration'`
    - **Impact**: Query rewrite, response format may change

13. **GET `/guest/purchase/training`** (Admin only)
    - **Current**: Queries `guest_training` table with pagination
    - **After Migration**: Query unified `payment` table with `WHERE payment_type='guest' AND service_type='training'`
    - **Impact**: Query rewrite, response format may change

#### Dashboard Controller (`/dashboard`) - Admin Only

14. **GET `/dashboard`** (Admin only)
    - **Current**: Complex raw SQL query aggregating data from all 8 payment tables
    - **After Migration**: Simplified query using unified `payment` table with GROUP BY service_type
    - **Impact**: Complete query rewrite, significant simplification

### 🟢 **Medium Impact Endpoints (User Profile)**

#### User Controller (`/user`)

15. **GET `/user/admin/:userId`** (Admin only)
    - **Current**: Calculates `total_spent` using UNION ALL across 4 user payment tables
    - **After Migration**: Single query on unified `payment` table with `WHERE userId=:id AND payment_type='user'`
    - **Impact**: Query simplification, performance improvement

### API Response Format Changes

#### Current Response Format (Example - Mentor Service)
```json
{
  "id": "clx123...",
  "amount": 100,
  "status": "paid",
  "progress": "Pending",
  "userId": "user123",
  "serviceId": "service123",
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z",
  "mentor_services": {
    "name": "Career Consultation",
    "mentor": {
      "name": "John Doe"
    }
  }
}
```

#### New Response Format (After Migration)
```json
{
  "id": "clx123...",
  "amount": 100,
  "status": "paid",
  "payment_type": "user",
  "service_type": "mentor",
  "progress": "Pending",
  "userId": "user123",
  "serviceId": "service123",
  "packageId": null,
  "immigration_serviceId": null,
  "trainingId": null,
  "guest_name": null,
  "guest_email": null,
  "guest_mobile": null,
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z",
  "service": {
    "name": "Career Consultation",
    "mentor": {
      "name": "John Doe"
    }
  }
}
```

### Backward Compatibility Strategy

#### Option 1: API Versioning (Recommended)
```typescript
// v1 endpoints (legacy)
@Controller('v1/payment')
export class PaymentV1Controller {
  // Keep old response format for backward compatibility
}

// v2 endpoints (new unified)
@Controller('v2/payment')
export class PaymentV2Controller {
  // New unified response format
}
```

#### Option 2: Response Transformation Layer
```typescript
// Transform unified payment response to match old format
private transformToLegacyFormat(payment: Payment, serviceType: string) {
  const legacyResponse = {
    id: payment.id,
    amount: payment.amount,
    status: payment.status,
    progress: payment.progress,
    createdAt: payment.createdAt,
    updatedAt: payment.updatedAt,
  };

  if (payment.payment_type === 'user') {
    legacyResponse.userId = payment.userId;
  } else {
    legacyResponse.name = payment.guest_name;
    legacyResponse.email = payment.guest_email;
    legacyResponse.mobile_no = payment.guest_mobile;
  }

  // Add service-specific fields
  switch (serviceType) {
    case 'mentor':
      legacyResponse.serviceId = payment.serviceId;
      legacyResponse.mentor_services = payment.service;
      break;
    case 'package':
      legacyResponse.packageId = payment.packageId;
      legacyResponse.package = payment.package;
      break;
    // ... other cases
  }

  return legacyResponse;
}
```

### Migration Implementation Plan for Endpoints

#### Phase 1: Preparation
1. **Create New Service Methods**: Implement unified payment methods alongside existing ones
2. **Add Response Transformers**: Create transformation layer for backward compatibility
3. **Update DTOs**: Modify or create new DTOs for unified payment structure
4. **Add Feature Flags**: Implement feature flags to switch between old and new implementations

#### Phase 2: Dual Implementation
1. **Implement Dual Write**: Write to both old and new tables during transition
2. **Add New Endpoints**: Create v2 endpoints with new unified structure
3. **Update Tests**: Create tests for both old and new implementations
4. **Documentation**: Update API documentation with migration notes

#### Phase 3: Switch Over
1. **Enable New Implementation**: Switch feature flags to use new unified table
2. **Monitor Performance**: Track API response times and error rates
3. **Validate Responses**: Ensure response formats match expectations
4. **Gradual Rollout**: Use feature flags for gradual user migration

#### Phase 4: Cleanup
1. **Remove Old Endpoints**: Deprecate and remove v1 endpoints after migration period
2. **Clean Up Code**: Remove old service methods and transformation layers
3. **Update Documentation**: Finalize API documentation with new structure
4. **Performance Optimization**: Optimize queries and indexes for new structure

### Endpoint Testing Strategy

#### Pre-Migration Testing
1. **Create Test Suite for Current Endpoints**
   ```bash
   # Test all current payment endpoints
   npm run test:e2e -- --grep "payment"
   npm run test:e2e -- --grep "guest"
   npm run test:e2e -- --grep "dashboard"
   ```

2. **Performance Baseline Testing**
   ```bash
   # Measure current API response times
   npm run test:performance -- payment-endpoints
   ```

3. **Load Testing**
   ```bash
   # Test current system under load
   npm run test:load -- --endpoints payment
   ```

#### During Migration Testing
1. **Dual Implementation Testing**
   ```typescript
   // Test both old and new implementations
   describe('Payment Migration Tests', () => {
     it('should return same data from old and new endpoints', async () => {
       const oldResponse = await request(app).get('/v1/payment/mentor-service');
       const newResponse = await request(app).get('/v2/payment/create');

       expect(transformResponse(newResponse)).toEqual(oldResponse);
     });
   });
   ```

2. **Data Consistency Testing**
   ```typescript
   // Verify data consistency between old and new tables
   describe('Data Consistency Tests', () => {
     it('should have consistent data in old and new tables', async () => {
       const oldData = await getOldTableData();
       const newData = await getNewTableData();

       expect(newData.length).toEqual(oldData.length);
       // Additional consistency checks...
     });
   });
   ```

#### Post-Migration Testing
1. **Regression Testing**
   ```bash
   # Run full test suite to ensure no regressions
   npm run test:e2e
   npm run test:integration
   npm run test:unit
   ```

2. **Performance Validation**
   ```bash
   # Compare performance before and after migration
   npm run test:performance:compare
   ```

3. **User Acceptance Testing**
   - Test all payment flows manually
   - Verify email notifications work correctly
   - Check admin dashboard functionality
   - Validate reporting accuracy

### API Documentation Updates

#### Swagger Documentation Changes
1. **Update API Descriptions**
   ```typescript
   @ApiOperation({
     summary: 'Create unified payment',
     description: 'Creates payment record in unified payment table. Supports all service types.',
     deprecated: false, // Mark old endpoints as deprecated
   })
   ```

2. **Add Migration Notes**
   ```typescript
   @ApiTags('payment-v2')
   @Controller('v2/payment')
   export class UnifiedPaymentController {
     // New unified endpoints
   }

   @ApiTags('payment-legacy')
   @Controller('payment')
   export class PaymentController {
     @ApiOperation({
       deprecated: true,
       description: 'Legacy endpoint. Use /v2/payment/create instead.',
     })
     // Old endpoints marked as deprecated
   }
   ```

3. **Response Schema Updates**
   ```typescript
   @ApiResponse({
     status: 200,
     description: 'Payment created successfully',
     schema: {
       type: 'object',
       properties: {
         id: { type: 'string' },
         amount: { type: 'number' },
         status: { type: 'string' },
         payment_type: { type: 'string', enum: ['user', 'guest'] },
         service_type: { type: 'string', enum: ['mentor', 'package', 'immigration', 'training'] },
         // ... other properties
       },
     },
   })
   ```

### Client Application Impact

#### Frontend Changes Required
1. **API Endpoint Updates**
   ```javascript
   // Old API calls
   const response = await fetch('/api/payment/mentor-service', {
     method: 'POST',
     body: JSON.stringify(data),
   });

   // New API calls (after migration)
   const response = await fetch('/api/v2/payment/create', {
     method: 'POST',
     body: JSON.stringify({
       ...data,
       service_type: 'mentor',
     }),
   });
   ```

2. **Response Handling Updates**
   ```javascript
   // Old response structure
   const { mentor_services, serviceId } = response.data;

   // New response structure
   const { service, serviceId, service_type } = response.data;
   ```

3. **Admin Dashboard Updates**
   ```javascript
   // Old dashboard API calls
   const mentorRevenue = await fetch('/api/guest/purchase/service');
   const packageRevenue = await fetch('/api/guest/purchase/package');

   // New unified API call
   const allRevenue = await fetch('/api/v2/payment/admin/stats/revenue');
   ```

#### Mobile App Changes
1. **API Integration Updates**
   - Update API base URLs to use v2 endpoints
   - Modify request/response handling for new structure
   - Update error handling for new error formats

2. **Data Model Updates**
   - Update local data models to match new API structure
   - Modify database schemas for offline storage
   - Update synchronization logic

### Third-Party Integration Impact

#### Stripe Webhook Changes
1. **Webhook Handler Updates**
   ```typescript
   // Old webhook handler
   async handleWebhook(event) {
     switch (event.data.object.metadata.type) {
       case 'mentor_service':
         await this.createUserMentorService(data);
         break;
       case 'guest-mentor':
         await this.createGuestMentorService(data);
         break;
       // ... 8 different cases
     }
   }

   // New unified webhook handler
   async handleWebhook(event) {
     const metadata = event.data.object.metadata;
     await this.unifiedPaymentService.createPayment({
       payment_type: metadata.type.startsWith('guest-') ? 'guest' : 'user',
       service_type: this.extractServiceType(metadata.type),
       // ... unified data structure
     });
   }
   ```

2. **Metadata Structure Changes**
   ```javascript
   // Old Stripe metadata
   {
     type: 'mentor_service', // or 'guest-mentor', 'package', etc.
     serviceId: 'service123',
     userId: 'user123',
   }

   // New unified metadata
   {
     payment_type: 'user', // or 'guest'
     service_type: 'mentor', // or 'package', 'immigration', 'training'
     serviceId: 'service123',
     userId: 'user123',
   }
   ```

#### External Reporting Tools
1. **Database Query Updates**
   - Update BI tool queries to use new unified table
   - Modify reporting dashboards to use new schema
   - Update data export scripts

2. **Analytics Integration**
   - Update analytics tracking to use new event structure
   - Modify conversion funnel tracking
   - Update revenue attribution models

### Monitoring and Alerting Updates

#### Application Monitoring
1. **Endpoint Monitoring**
   ```yaml
   # monitoring/endpoints.yml
   endpoints:
     - name: "Payment Creation"
       url: "/v2/payment/create"
       method: "POST"
       expected_status: 200
       timeout: 5000

     - name: "Legacy Payment (Deprecated)"
       url: "/payment/mentor-service"
       method: "POST"
       expected_status: 200
       timeout: 5000
       deprecated: true
   ```

2. **Database Monitoring**
   ```sql
   -- Monitor unified payment table performance
   SELECT
     schemaname,
     tablename,
     attname,
     n_distinct,
     correlation
   FROM pg_stats
   WHERE tablename = 'payment';
   ```

3. **Error Tracking**
   ```typescript
   // Update error tracking for new endpoints
   logger.error('Payment creation failed', {
     endpoint: '/v2/payment/create',
     payment_type: data.payment_type,
     service_type: data.service_type,
     error: error.message,
   });
   ```

#### Business Metrics Monitoring
1. **Revenue Tracking**
   ```sql
   -- New unified revenue query
   SELECT
     service_type,
     payment_type,
     DATE_TRUNC('day', "createdAt") as date,
     SUM(amount) as daily_revenue,
     COUNT(*) as transaction_count
   FROM payment
   WHERE status = 'paid'
   GROUP BY service_type, payment_type, DATE_TRUNC('day', "createdAt")
   ORDER BY date DESC;
   ```

2. **Conversion Tracking**
   ```sql
   -- Track conversion rates by service type
   SELECT
     service_type,
     COUNT(*) as total_attempts,
     COUNT(CASE WHEN status = 'paid' THEN 1 END) as successful_payments,
     ROUND(
       COUNT(CASE WHEN status = 'paid' THEN 1 END) * 100.0 / COUNT(*),
       2
     ) as conversion_rate
   FROM payment
   GROUP BY service_type;
   ```
