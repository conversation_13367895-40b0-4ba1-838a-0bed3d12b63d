/*
  Warnings:

  - You are about to drop the column `adminId` on the `service` table. All the data in the column will be lost.
  - You are about to drop the column `rejectionReason` on the `service` table. All the data in the column will be lost.
  - Added the required column `desc` to the `mentor` table without a default value. This is not possible if the table is not empty.
  - Made the column `emailVerified` on table `mentor` required. This step will fail if there are existing NULL values in that column.
  - Made the column `image` on table `mentor` required. This step will fail if there are existing NULL values in that column.
  - Made the column `designation` on table `mentor` required. This step will fail if there are existing NULL values in that column.
  - Added the required column `rating` to the `review` table without a default value. This is not possible if the table is not empty.
  - Added the required column `meeting_link` to the `service` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "service" DROP CONSTRAINT "service_adminId_fkey";

-- AlterTable
ALTER TABLE "mentor" ADD COLUMN     "desc" TEXT NOT NULL,
ADD COLUMN     "status" "Status" NOT NULL DEFAULT 'Pending',
ALTER COLUMN "emailVerified" SET NOT NULL,
ALTER COLUMN "image" SET NOT NULL,
ALTER COLUMN "designation" SET NOT NULL;

-- AlterTable
ALTER TABLE "review" ADD COLUMN     "rating" INTEGER NOT NULL;

-- AlterTable
ALTER TABLE "service" DROP COLUMN "adminId",
DROP COLUMN "rejectionReason",
ADD COLUMN     "meeting_link" TEXT NOT NULL;
