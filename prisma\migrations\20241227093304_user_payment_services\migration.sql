-- CreateTable
CREATE TABLE "user_package" (
    "id" TEXT NOT NULL,
    "amount" INTEGER NOT NULL,
    "status" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT NOT NULL,
    "packageId" TEXT NOT NULL,

    CONSTRAINT "user_package_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_immigration_service" (
    "id" TEXT NOT NULL,
    "amount" INTEGER NOT NULL,
    "status" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT NOT NULL,
    "immigration_serviceId" TEXT NOT NULL,

    CONSTRAINT "user_immigration_service_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "user_package" ADD CONSTRAINT "user_package_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_package" ADD CONSTRAINT "user_package_packageId_fkey" FOREIGN KEY ("packageId") REFERENCES "packages"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_immigration_service" ADD CONSTRAINT "user_immigration_service_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_immigration_service" ADD CONSTRAINT "user_immigration_service_immigration_serviceId_fkey" FOREIGN KEY ("immigration_serviceId") REFERENCES "immigration_service"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
