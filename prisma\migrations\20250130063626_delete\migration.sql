-- DropForeignKey
ALTER TABLE "guest_immigration_service" DROP CONSTRAINT "guest_immigration_service_immigration_serviceId_fkey";

-- DropForeignKey
ALTER TABLE "guest_mentor_service" DROP CONSTRAINT "guest_mentor_service_serviceId_fkey";

-- DropForeignKey
ALTER TABLE "guest_package" DROP CONSTRAINT "guest_package_packageId_fkey";

-- DropForeignKey
ALTER TABLE "guest_training" DROP CONSTRAINT "guest_training_trainingId_fkey";

-- DropForeignKey
ALTER TABLE "review" DROP CONSTRAINT "review_mentorId_fkey";

-- DropForeignKey
ALTER TABLE "review" DROP CONSTRAINT "review_userId_fkey";

-- DropForeignKey
ALTER TABLE "service" DROP CONSTRAINT "service_mentorId_fkey";

-- DropForeignKey
ALTER TABLE "user_immigration_service" DROP CONSTRAINT "user_immigration_service_immigration_serviceId_fkey";

-- DropFore<PERSON><PERSON>ey
ALTER TABLE "user_immigration_service" DROP CONSTRAINT "user_immigration_service_userId_fkey";

-- DropForeignKey
ALTER TABLE "user_mentor_service" DROP CONSTRAINT "user_mentor_service_serviceId_fkey";

-- DropForeignKey
ALTER TABLE "user_mentor_service" DROP CONSTRAINT "user_mentor_service_userId_fkey";

-- DropForeignKey
ALTER TABLE "user_package" DROP CONSTRAINT "user_package_packageId_fkey";

-- DropForeignKey
ALTER TABLE "user_package" DROP CONSTRAINT "user_package_userId_fkey";

-- DropForeignKey
ALTER TABLE "user_training" DROP CONSTRAINT "user_training_trainingId_fkey";

-- DropForeignKey
ALTER TABLE "user_training" DROP CONSTRAINT "user_training_userId_fkey";

-- AlterTable
ALTER TABLE "guest_immigration_service" ALTER COLUMN "immigration_serviceId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "guest_mentor_service" ALTER COLUMN "serviceId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "guest_package" ALTER COLUMN "packageId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "guest_training" ALTER COLUMN "trainingId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "review" ALTER COLUMN "mentorId" DROP NOT NULL,
ALTER COLUMN "userId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "service" ALTER COLUMN "mentorId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "user_immigration_service" ALTER COLUMN "userId" DROP NOT NULL,
ALTER COLUMN "immigration_serviceId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "user_mentor_service" ALTER COLUMN "userId" DROP NOT NULL,
ALTER COLUMN "serviceId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "user_package" ALTER COLUMN "userId" DROP NOT NULL,
ALTER COLUMN "packageId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "user_training" ALTER COLUMN "userId" DROP NOT NULL,
ALTER COLUMN "trainingId" DROP NOT NULL;

-- AddForeignKey
ALTER TABLE "service" ADD CONSTRAINT "service_mentorId_fkey" FOREIGN KEY ("mentorId") REFERENCES "mentor"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "review" ADD CONSTRAINT "review_mentorId_fkey" FOREIGN KEY ("mentorId") REFERENCES "mentor"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "review" ADD CONSTRAINT "review_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_mentor_service" ADD CONSTRAINT "user_mentor_service_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_mentor_service" ADD CONSTRAINT "user_mentor_service_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "guest_mentor_service" ADD CONSTRAINT "guest_mentor_service_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "service"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_package" ADD CONSTRAINT "user_package_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_package" ADD CONSTRAINT "user_package_packageId_fkey" FOREIGN KEY ("packageId") REFERENCES "packages"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "guest_package" ADD CONSTRAINT "guest_package_packageId_fkey" FOREIGN KEY ("packageId") REFERENCES "packages"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_immigration_service" ADD CONSTRAINT "user_immigration_service_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_immigration_service" ADD CONSTRAINT "user_immigration_service_immigration_serviceId_fkey" FOREIGN KEY ("immigration_serviceId") REFERENCES "immigration_service"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "guest_immigration_service" ADD CONSTRAINT "guest_immigration_service_immigration_serviceId_fkey" FOREIGN KEY ("immigration_serviceId") REFERENCES "immigration_service"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_training" ADD CONSTRAINT "user_training_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_training" ADD CONSTRAINT "user_training_trainingId_fkey" FOREIGN KEY ("trainingId") REFERENCES "training"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "guest_training" ADD CONSTRAINT "guest_training_trainingId_fkey" FOREIGN KEY ("trainingId") REFERENCES "training"("id") ON DELETE SET NULL ON UPDATE CASCADE;
