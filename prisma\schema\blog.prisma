model blog {
    id        String    @id @default(cuid())
    title     String
    slug      String    @unique
    summary   String
    blogger   String
    img       String
    desc      String
    createdAt DateTime  @default(now())
    updatedAt DateTime  @updatedAt
    comments  comment[]
}

model comment {
    id        String   @id @default(cuid())
    content   String
    blog      blog     @relation(fields: [blogId], references: [id])
    blogId    String
    author    user     @relation(fields: [authorId], references: [id])
    authorId  String
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
    parentId  String? // For nested comments/replies
    replies   comment[] @relation("CommentReplies")
    parent    comment? @relation("CommentReplies", fields: [parentId], references: [id]) // Parent comment
}
