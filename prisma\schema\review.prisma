model review {
  id        String   @id @default(cuid())
  message   String
  rating    Int
  mentor    mentor?  @relation(fields: [mentorId], references: [id], onDelete: SetNull)
  mentorId  String?
  user      user?    @relation(fields: [userId], references: [id], onDelete: SetNull)
  userId    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model customer_review {
  id        String   @id @default(cuid())
  name      String
  img       String?
  comment   String
  source    String
  rating    Int
  order     Int?
  date      DateTime @default(now())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
