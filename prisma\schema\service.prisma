model packages {
    id        String          @id @default(cuid())
    name      String
    note      String
    amount    Int
    order     Int?
    service   String[]
    createdAt DateTime        @default(now())
    updatedAt DateTime        @updatedAt
    users     user_package[]
    guest     guest_package[]
}

model immigration_service {
    id        String                      @id @default(cuid())
    name      String
    amount    Int
    order     Int?
    service   String[]
    createdAt DateTime                    @default(now())
    updatedAt DateTime                    @updatedAt
    users     user_immigration_service[]
    guest     guest_immigration_service[]
}

model training {
    id         String          @id @default(cuid())
    name       String
    img        String
    amount     Int
    order      Int?
    service    String[]
    highlights String[]
    createdAt  DateTime        @default(now())
    updatedAt  DateTime        @updatedAt
    users      user_training[]

    guest guest_training[]
}
