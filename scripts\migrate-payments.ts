import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface MigrationStats {
  userMentorServices: number;
  guestMentorServices: number;
  userPackages: number;
  guestPackages: number;
  userImmigrationServices: number;
  guestImmigrationServices: number;
  userTraining: number;
  guestTraining: number;
  total: number;
}

async function migratePayments(): Promise<MigrationStats> {
  console.log('🚀 Starting payment migration...');
  
  const stats: MigrationStats = {
    userMentorServices: 0,
    guestMentorServices: 0,
    userPackages: 0,
    guestPackages: 0,
    userImmigrationServices: 0,
    guestImmigrationServices: 0,
    userTraining: 0,
    guestTraining: 0,
    total: 0,
  };

  try {
    // Start transaction for data consistency
    await prisma.$transaction(async (tx) => {
      // Migrate user_mentor_service
      stats.userMentorServices = await migrateUserMentorServices(tx);
      
      // Migrate guest_mentor_service
      stats.guestMentorServices = await migrateGuestMentorServices(tx);
      
      // Migrate user_package
      stats.userPackages = await migrateUserPackages(tx);
      
      // Migrate guest_package
      stats.guestPackages = await migrateGuestPackages(tx);
      
      // Migrate user_immigration_service
      stats.userImmigrationServices = await migrateUserImmigrationServices(tx);
      
      // Migrate guest_immigration_service
      stats.guestImmigrationServices = await migrateGuestImmigrationServices(tx);
      
      // Migrate user_training
      stats.userTraining = await migrateUserTraining(tx);
      
      // Migrate guest_training
      stats.guestTraining = await migrateGuestTraining(tx);
    });

    stats.total = Object.values(stats).reduce((sum, count) => sum + count, 0) - stats.total;
    
    console.log('✅ Payment migration completed successfully!');
    console.log('📊 Migration Statistics:', stats);
    
    return stats;
  } catch (error) {
    console.error('❌ Payment migration failed:', error);
    throw error;
  }
}

async function migrateUserMentorServices(tx: any): Promise<number> {
  console.log('📦 Migrating user_mentor_service records...');
  
  const records = await tx.user_mentor_service.findMany();
  
  for (const record of records) {
    await tx.payment.create({
      data: {
        id: record.id,
        amount: record.amount,
        status: record.status,
        payment_type: 'user',
        service_type: 'mentor',
        progress: record.progress,
        userId: record.userId,
        serviceId: record.serviceId,
        createdAt: record.createdAt,
        updatedAt: record.updatedAt,
      },
    });
  }
  
  console.log(`✅ Migrated ${records.length} user mentor service records`);
  return records.length;
}

async function migrateGuestMentorServices(tx: any): Promise<number> {
  console.log('📦 Migrating guest_mentor_service records...');
  
  const records = await tx.guest_mentor_service.findMany();
  
  for (const record of records) {
    await tx.payment.create({
      data: {
        id: record.id,
        amount: record.amount,
        status: record.status,
        payment_type: 'guest',
        service_type: 'mentor',
        progress: record.progress,
        serviceId: record.serviceId,
        guest_name: record.name,
        guest_email: record.email,
        guest_mobile: record.mobile_no,
        createdAt: record.createdAt,
        updatedAt: record.updatedAt,
      },
    });
  }
  
  console.log(`✅ Migrated ${records.length} guest mentor service records`);
  return records.length;
}

async function migrateUserPackages(tx: any): Promise<number> {
  console.log('📦 Migrating user_package records...');
  
  const records = await tx.user_package.findMany();
  
  for (const record of records) {
    await tx.payment.create({
      data: {
        id: record.id,
        amount: record.amount,
        status: record.status,
        payment_type: 'user',
        service_type: 'package',
        progress: record.progress || 'Pending',
        userId: record.userId,
        packageId: record.packageId,
        createdAt: record.createdAt,
        updatedAt: record.updatedAt,
      },
    });
  }
  
  console.log(`✅ Migrated ${records.length} user package records`);
  return records.length;
}

async function migrateGuestPackages(tx: any): Promise<number> {
  console.log('📦 Migrating guest_package records...');
  
  const records = await tx.guest_package.findMany();
  
  for (const record of records) {
    await tx.payment.create({
      data: {
        id: record.id,
        amount: record.amount,
        status: record.status,
        payment_type: 'guest',
        service_type: 'package',
        progress: record.progress,
        packageId: record.packageId,
        guest_name: record.name,
        guest_email: record.email,
        guest_mobile: record.mobile_no,
        createdAt: record.createdAt,
        updatedAt: record.updatedAt,
      },
    });
  }
  
  console.log(`✅ Migrated ${records.length} guest package records`);
  return records.length;
}

async function migrateUserImmigrationServices(tx: any): Promise<number> {
  console.log('📦 Migrating user_immigration_service records...');
  
  const records = await tx.user_immigration_service.findMany();
  
  for (const record of records) {
    await tx.payment.create({
      data: {
        id: record.id,
        amount: record.amount,
        status: record.status,
        payment_type: 'user',
        service_type: 'immigration',
        progress: record.progress,
        userId: record.userId,
        immigration_serviceId: record.immigration_serviceId,
        createdAt: record.createdAt,
        updatedAt: record.updatedAt,
      },
    });
  }
  
  console.log(`✅ Migrated ${records.length} user immigration service records`);
  return records.length;
}

async function migrateGuestImmigrationServices(tx: any): Promise<number> {
  console.log('📦 Migrating guest_immigration_service records...');
  
  const records = await tx.guest_immigration_service.findMany();
  
  for (const record of records) {
    await tx.payment.create({
      data: {
        id: record.id,
        amount: record.amount,
        status: record.status,
        payment_type: 'guest',
        service_type: 'immigration',
        progress: record.progress,
        immigration_serviceId: record.immigration_serviceId,
        guest_name: record.name,
        guest_email: record.email,
        guest_mobile: record.mobile_no,
        createdAt: record.createdAt,
        updatedAt: record.updatedAt,
      },
    });
  }
  
  console.log(`✅ Migrated ${records.length} guest immigration service records`);
  return records.length;
}

async function migrateUserTraining(tx: any): Promise<number> {
  console.log('📦 Migrating user_training records...');
  
  const records = await tx.user_training.findMany();
  
  for (const record of records) {
    await tx.payment.create({
      data: {
        id: record.id,
        amount: record.amount,
        status: record.status,
        payment_type: 'user',
        service_type: 'training',
        progress: record.progress,
        userId: record.userId,
        trainingId: record.trainingId,
        createdAt: record.createdAt,
        updatedAt: record.updatedAt,
      },
    });
  }
  
  console.log(`✅ Migrated ${records.length} user training records`);
  return records.length;
}

async function migrateGuestTraining(tx: any): Promise<number> {
  console.log('📦 Migrating guest_training records...');
  
  const records = await tx.guest_training.findMany();
  
  for (const record of records) {
    await tx.payment.create({
      data: {
        id: record.id,
        amount: record.amount,
        status: record.status,
        payment_type: 'guest',
        service_type: 'training',
        progress: record.progress,
        trainingId: record.trainingId,
        guest_name: record.name,
        guest_email: record.email,
        guest_mobile: record.mobile_no,
        createdAt: record.createdAt,
        updatedAt: record.updatedAt,
      },
    });
  }
  
  console.log(`✅ Migrated ${records.length} guest training records`);
  return records.length;
}

// Validation function to ensure migration integrity
async function validateMigration(): Promise<boolean> {
  console.log('🔍 Validating migration...');
  
  try {
    const oldTableCounts = await Promise.all([
      prisma.user_mentor_service.count(),
      prisma.guest_mentor_service.count(),
      prisma.user_package.count(),
      prisma.guest_package.count(),
      prisma.user_immigration_service.count(),
      prisma.guest_immigration_service.count(),
      prisma.user_training.count(),
      prisma.guest_training.count(),
    ]);
    
    const totalOldRecords = oldTableCounts.reduce((sum, count) => sum + count, 0);
    const newTableCount = await prisma.payment.count();
    
    if (totalOldRecords !== newTableCount) {
      console.error(`❌ Validation failed: ${totalOldRecords} old records vs ${newTableCount} new records`);
      return false;
    }
    
    // Validate data integrity for each service type
    const validationResults = await Promise.all([
      validateServiceType('mentor'),
      validateServiceType('package'),
      validateServiceType('immigration'),
      validateServiceType('training'),
    ]);
    
    const allValid = validationResults.every(result => result);
    
    if (allValid) {
      console.log('✅ Migration validation passed!');
      return true;
    } else {
      console.error('❌ Migration validation failed!');
      return false;
    }
  } catch (error) {
    console.error('❌ Validation error:', error);
    return false;
  }
}

async function validateServiceType(serviceType: string): Promise<boolean> {
  const userPayments = await prisma.payment.count({
    where: {
      service_type: serviceType,
      payment_type: 'user',
    },
  });
  
  const guestPayments = await prisma.payment.count({
    where: {
      service_type: serviceType,
      payment_type: 'guest',
    },
  });
  
  console.log(`📊 ${serviceType}: ${userPayments} user payments, ${guestPayments} guest payments`);
  return true;
}

// Main execution
if (require.main === module) {
  migratePayments()
    .then(async (stats) => {
      const isValid = await validateMigration();
      if (isValid) {
        console.log('🎉 Migration completed successfully with validation!');
        process.exit(0);
      } else {
        console.error('💥 Migration completed but validation failed!');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('💥 Migration failed:', error);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { migratePayments, validateMigration };
