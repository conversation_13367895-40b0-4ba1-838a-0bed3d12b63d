import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface RollbackStats {
  userMentorServices: number;
  guestMentorServices: number;
  userPackages: number;
  guestPackages: number;
  userImmigrationServices: number;
  guestImmigrationServices: number;
  userTraining: number;
  guestTraining: number;
  total: number;
  success: boolean;
}

async function rollbackPaymentMigration(): Promise<RollbackStats> {
  console.log('🔄 Starting payment migration rollback...');
  console.log('⚠️  WARNING: This will restore data from the unified payment table back to the original 8 tables');
  
  const stats: RollbackStats = {
    userMentorServices: 0,
    guestMentorServices: 0,
    userPackages: 0,
    guestPackages: 0,
    userImmigrationServices: 0,
    guestImmigrationServices: 0,
    userTraining: 0,
    guestTraining: 0,
    total: 0,
    success: false,
  };

  try {
    // Verify that the payment table exists and has data
    const paymentCount = await prisma.payment.count();
    if (paymentCount === 0) {
      throw new Error('No data found in payment table to rollback');
    }
    
    console.log(`📊 Found ${paymentCount} records in payment table to rollback`);
    
    // Start transaction for data consistency
    await prisma.$transaction(async (tx) => {
      // Clear existing data in old tables (if any)
      await clearOldTables(tx);
      
      // Rollback user_mentor_service
      stats.userMentorServices = await rollbackUserMentorServices(tx);
      
      // Rollback guest_mentor_service
      stats.guestMentorServices = await rollbackGuestMentorServices(tx);
      
      // Rollback user_package
      stats.userPackages = await rollbackUserPackages(tx);
      
      // Rollback guest_package
      stats.guestPackages = await rollbackGuestPackages(tx);
      
      // Rollback user_immigration_service
      stats.userImmigrationServices = await rollbackUserImmigrationServices(tx);
      
      // Rollback guest_immigration_service
      stats.guestImmigrationServices = await rollbackGuestImmigrationServices(tx);
      
      // Rollback user_training
      stats.userTraining = await rollbackUserTraining(tx);
      
      // Rollback guest_training
      stats.guestTraining = await rollbackGuestTraining(tx);
    });

    stats.total = Object.values(stats).reduce((sum, count) => 
      typeof count === 'number' ? sum + count : sum, 0) - stats.total;
    stats.success = true;
    
    console.log('✅ Payment migration rollback completed successfully!');
    console.log('📊 Rollback Statistics:', stats);
    
    return stats;
  } catch (error) {
    console.error('❌ Payment migration rollback failed:', error);
    stats.success = false;
    throw error;
  }
}

async function clearOldTables(tx: any): Promise<void> {
  console.log('🧹 Clearing existing data in old tables...');
  
  await tx.user_mentor_service.deleteMany({});
  await tx.guest_mentor_service.deleteMany({});
  await tx.user_package.deleteMany({});
  await tx.guest_package.deleteMany({});
  await tx.user_immigration_service.deleteMany({});
  await tx.guest_immigration_service.deleteMany({});
  await tx.user_training.deleteMany({});
  await tx.guest_training.deleteMany({});
  
  console.log('✅ Old tables cleared');
}

async function rollbackUserMentorServices(tx: any): Promise<number> {
  console.log('📦 Rolling back user_mentor_service records...');
  
  const payments = await tx.payment.findMany({
    where: {
      payment_type: 'user',
      service_type: 'mentor',
    },
  });
  
  for (const payment of payments) {
    await tx.user_mentor_service.create({
      data: {
        id: payment.id,
        amount: payment.amount,
        status: payment.status,
        progress: payment.progress,
        userId: payment.userId,
        serviceId: payment.serviceId,
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt,
      },
    });
  }
  
  console.log(`✅ Rolled back ${payments.length} user mentor service records`);
  return payments.length;
}

async function rollbackGuestMentorServices(tx: any): Promise<number> {
  console.log('📦 Rolling back guest_mentor_service records...');
  
  const payments = await tx.payment.findMany({
    where: {
      payment_type: 'guest',
      service_type: 'mentor',
    },
  });
  
  for (const payment of payments) {
    await tx.guest_mentor_service.create({
      data: {
        id: payment.id,
        amount: payment.amount,
        status: payment.status,
        progress: payment.progress,
        serviceId: payment.serviceId,
        name: payment.guest_name,
        email: payment.guest_email,
        mobile_no: payment.guest_mobile,
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt,
      },
    });
  }
  
  console.log(`✅ Rolled back ${payments.length} guest mentor service records`);
  return payments.length;
}

async function rollbackUserPackages(tx: any): Promise<number> {
  console.log('📦 Rolling back user_package records...');
  
  const payments = await tx.payment.findMany({
    where: {
      payment_type: 'user',
      service_type: 'package',
    },
  });
  
  for (const payment of payments) {
    await tx.user_package.create({
      data: {
        id: payment.id,
        amount: payment.amount,
        status: payment.status,
        userId: payment.userId,
        packageId: payment.packageId,
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt,
      },
    });
  }
  
  console.log(`✅ Rolled back ${payments.length} user package records`);
  return payments.length;
}

async function rollbackGuestPackages(tx: any): Promise<number> {
  console.log('📦 Rolling back guest_package records...');
  
  const payments = await tx.payment.findMany({
    where: {
      payment_type: 'guest',
      service_type: 'package',
    },
  });
  
  for (const payment of payments) {
    await tx.guest_package.create({
      data: {
        id: payment.id,
        amount: payment.amount,
        status: payment.status,
        progress: payment.progress,
        packageId: payment.packageId,
        name: payment.guest_name,
        email: payment.guest_email,
        mobile_no: payment.guest_mobile,
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt,
      },
    });
  }
  
  console.log(`✅ Rolled back ${payments.length} guest package records`);
  return payments.length;
}

async function rollbackUserImmigrationServices(tx: any): Promise<number> {
  console.log('📦 Rolling back user_immigration_service records...');
  
  const payments = await tx.payment.findMany({
    where: {
      payment_type: 'user',
      service_type: 'immigration',
    },
  });
  
  for (const payment of payments) {
    await tx.user_immigration_service.create({
      data: {
        id: payment.id,
        amount: payment.amount,
        status: payment.status,
        progress: payment.progress,
        userId: payment.userId,
        immigration_serviceId: payment.immigration_serviceId,
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt,
      },
    });
  }
  
  console.log(`✅ Rolled back ${payments.length} user immigration service records`);
  return payments.length;
}

async function rollbackGuestImmigrationServices(tx: any): Promise<number> {
  console.log('📦 Rolling back guest_immigration_service records...');
  
  const payments = await tx.payment.findMany({
    where: {
      payment_type: 'guest',
      service_type: 'immigration',
    },
  });
  
  for (const payment of payments) {
    await tx.guest_immigration_service.create({
      data: {
        id: payment.id,
        amount: payment.amount,
        status: payment.status,
        progress: payment.progress,
        immigration_serviceId: payment.immigration_serviceId,
        name: payment.guest_name,
        email: payment.guest_email,
        mobile_no: payment.guest_mobile,
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt,
      },
    });
  }
  
  console.log(`✅ Rolled back ${payments.length} guest immigration service records`);
  return payments.length;
}

async function rollbackUserTraining(tx: any): Promise<number> {
  console.log('📦 Rolling back user_training records...');
  
  const payments = await tx.payment.findMany({
    where: {
      payment_type: 'user',
      service_type: 'training',
    },
  });
  
  for (const payment of payments) {
    await tx.user_training.create({
      data: {
        id: payment.id,
        amount: payment.amount,
        status: payment.status,
        progress: payment.progress,
        userId: payment.userId,
        trainingId: payment.trainingId,
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt,
      },
    });
  }
  
  console.log(`✅ Rolled back ${payments.length} user training records`);
  return payments.length;
}

async function rollbackGuestTraining(tx: any): Promise<number> {
  console.log('📦 Rolling back guest_training records...');
  
  const payments = await tx.payment.findMany({
    where: {
      payment_type: 'guest',
      service_type: 'training',
    },
  });
  
  for (const payment of payments) {
    await tx.guest_training.create({
      data: {
        id: payment.id,
        amount: payment.amount,
        status: payment.status,
        progress: payment.progress,
        trainingId: payment.trainingId,
        name: payment.guest_name,
        email: payment.guest_email,
        mobile_no: payment.guest_mobile,
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt,
      },
    });
  }
  
  console.log(`✅ Rolled back ${payments.length} guest training records`);
  return payments.length;
}

async function validateRollback(): Promise<boolean> {
  console.log('🔍 Validating rollback...');
  
  try {
    const paymentCount = await prisma.payment.count();
    
    const oldTableCounts = await Promise.all([
      prisma.user_mentor_service.count(),
      prisma.guest_mentor_service.count(),
      prisma.user_package.count(),
      prisma.guest_package.count(),
      prisma.user_immigration_service.count(),
      prisma.guest_immigration_service.count(),
      prisma.user_training.count(),
      prisma.guest_training.count(),
    ]);
    
    const totalOldRecords = oldTableCounts.reduce((sum, count) => sum + count, 0);
    
    if (paymentCount !== totalOldRecords) {
      console.error(`❌ Rollback validation failed: ${paymentCount} payment records vs ${totalOldRecords} old table records`);
      return false;
    }
    
    console.log('✅ Rollback validation passed!');
    return true;
  } catch (error) {
    console.error('❌ Rollback validation error:', error);
    return false;
  }
}

// Main execution
if (require.main === module) {
  // Add confirmation prompt
  console.log('⚠️  WARNING: This will rollback the payment migration!');
  console.log('⚠️  This action will restore data from the unified payment table back to the original 8 tables.');
  console.log('⚠️  Make sure you have a backup before proceeding!');
  
  // In a real scenario, you'd want to add a confirmation prompt here
  // For now, we'll proceed directly
  
  rollbackPaymentMigration()
    .then(async (stats) => {
      const isValid = await validateRollback();
      if (isValid && stats.success) {
        console.log('🎉 Rollback completed successfully with validation!');
        process.exit(0);
      } else {
        console.error('💥 Rollback completed but validation failed!');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('💥 Rollback failed:', error);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { rollbackPaymentMigration, validateRollback };
