import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface RollbackStats {
  regularUsers: number;
  adminUsers: number;
  mentorUsers: number;
  superAdmins: number;
  total: number;
  errors: string[];
  success: boolean;
}

async function rollbackUserRoleMigration(): Promise<RollbackStats> {
  console.log('🔄 Starting user role migration rollback...');
  console.log('⚠️  WARNING: This will restore data from the unified user table back to the original 3 tables');
  
  const stats: RollbackStats = {
    regularUsers: 0,
    adminUsers: 0,
    mentorUsers: 0,
    superAdmins: 0,
    total: 0,
    errors: [],
    success: false,
  };

  try {
    // Verify that the unified_user table exists and has data
    const unifiedUserCount = await prisma.unified_user.count();
    if (unifiedUserCount === 0) {
      throw new Error('No data found in unified_user table to rollback');
    }
    
    console.log(`📊 Found ${unifiedUserCount} records in unified_user table to rollback`);
    
    // Start transaction for data consistency
    await prisma.$transaction(async (tx) => {
      // Clear existing data in old tables (if any)
      await clearOldTables(tx);
      
      // Rollback regular users
      stats.regularUsers = await rollbackRegularUsers(tx, stats);
      
      // Rollback admin users
      stats.adminUsers = await rollbackAdminUsers(tx, stats);
      
      // Rollback mentor users
      stats.mentorUsers = await rollbackMentorUsers(tx, stats);
      
      // Count super admins (they won't be rolled back to old tables)
      stats.superAdmins = await countSuperAdmins(tx);
    });

    stats.total = stats.regularUsers + stats.adminUsers + stats.mentorUsers;
    stats.success = true;
    
    console.log('✅ User role migration rollback completed successfully!');
    console.log('📊 Rollback Statistics:', stats);
    
    if (stats.superAdmins > 0) {
      console.log(`⚠️  Note: ${stats.superAdmins} super admin(s) remain in unified_user table (no equivalent in old schema)`);
    }
    
    return stats;
  } catch (error) {
    console.error('❌ User role migration rollback failed:', error);
    stats.success = false;
    stats.errors.push(error.message);
    throw error;
  }
}

async function clearOldTables(tx: any): Promise<void> {
  console.log('🧹 Clearing existing data in old tables...');
  
  // Clear old tables in correct order to avoid foreign key conflicts
  await tx.user.deleteMany({});
  await tx.admin.deleteMany({});
  await tx.mentor.deleteMany({});
  
  console.log('✅ Old tables cleared');
}

async function rollbackRegularUsers(tx: any, stats: RollbackStats): Promise<number> {
  console.log('📦 Rolling back regular users...');
  
  const users = await tx.unified_user.findMany({
    where: { role: 'user' },
  });
  
  for (const user of users) {
    try {
      // Extract original provider from metadata or use current provider
      let provider = user.provider || 'credentials';
      if (user.metadata && user.metadata.original_provider) {
        provider = user.metadata.original_provider;
      }
      
      await tx.user.create({
        data: {
          id: user.id,
          name: user.name,
          email: user.email,
          emailVerified: user.emailVerified,
          image: user.image,
          password: user.password,
          provider: provider,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
        },
      });
    } catch (error) {
      console.error(`❌ Failed to rollback user ${user.email}:`, error.message);
      stats.errors.push(`User ${user.email}: ${error.message}`);
    }
  }
  
  console.log(`✅ Rolled back ${users.length} regular users`);
  return users.length;
}

async function rollbackAdminUsers(tx: any, stats: RollbackStats): Promise<number> {
  console.log('📦 Rolling back admin users...');
  
  const admins = await tx.unified_user.findMany({
    where: { role: 'admin' },
  });
  
  for (const admin of admins) {
    try {
      await tx.admin.create({
        data: {
          id: admin.id,
          name: admin.name,
          email: admin.email,
          emailVerified: admin.emailVerified,
          image: admin.image,
          password: admin.password,
          createdAt: admin.createdAt,
          updatedAt: admin.updatedAt,
        },
      });
    } catch (error) {
      console.error(`❌ Failed to rollback admin ${admin.email}:`, error.message);
      stats.errors.push(`Admin ${admin.email}: ${error.message}`);
    }
  }
  
  console.log(`✅ Rolled back ${admins.length} admin users`);
  return admins.length;
}

async function rollbackMentorUsers(tx: any, stats: RollbackStats): Promise<number> {
  console.log('📦 Rolling back mentor users...');
  
  const mentors = await tx.unified_user.findMany({
    where: { role: 'mentor' },
  });
  
  for (const mentor of mentors) {
    try {
      // Convert status back to mentor status format
      let mentorStatus = 'Active';
      if (mentor.metadata && mentor.metadata.original_status) {
        mentorStatus = mentor.metadata.original_status;
      } else {
        // Map unified status to mentor status
        switch (mentor.status) {
          case 'active':
            mentorStatus = 'Active';
            break;
          case 'pending':
            mentorStatus = 'Pending';
            break;
          case 'inactive':
            mentorStatus = 'Inactive';
            break;
          case 'suspended':
            mentorStatus = 'Suspended';
            break;
          default:
            mentorStatus = 'Active';
        }
      }
      
      // Extract servicesId from metadata if available
      let servicesId = null;
      if (mentor.metadata && mentor.metadata.services_id) {
        servicesId = mentor.metadata.services_id;
      }
      
      await tx.mentor.create({
        data: {
          id: mentor.id,
          name: mentor.name,
          email: mentor.email,
          emailVerified: mentor.emailVerified,
          image: mentor.image,
          password: mentor.password,
          location: mentor.location,
          designation: mentor.designation,
          desc: mentor.description,
          order: mentor.display_order,
          linkedin: mentor.linkedin_url,
          profile: mentor.profile_url,
          status: mentorStatus,
          servicesId: servicesId,
          createdAt: mentor.createdAt,
          updatedAt: mentor.updatedAt,
        },
      });
    } catch (error) {
      console.error(`❌ Failed to rollback mentor ${mentor.email}:`, error.message);
      stats.errors.push(`Mentor ${mentor.email}: ${error.message}`);
    }
  }
  
  console.log(`✅ Rolled back ${mentors.length} mentor users`);
  return mentors.length;
}

async function countSuperAdmins(tx: any): Promise<number> {
  const superAdmins = await tx.unified_user.count({
    where: { role: 'super_admin' },
  });
  
  return superAdmins;
}

async function validateRollback(): Promise<boolean> {
  console.log('🔍 Validating rollback...');
  
  try {
    // Count records in unified_user table by role
    const roleCounts = await prisma.unified_user.groupBy({
      by: ['role'],
      _count: { id: true },
    });
    
    let unifiedUserCount = 0;
    let unifiedAdminCount = 0;
    let unifiedMentorCount = 0;
    let unifiedSuperAdminCount = 0;
    
    roleCounts.forEach(({ role, _count }) => {
      switch (role) {
        case 'user':
          unifiedUserCount = _count.id;
          break;
        case 'admin':
          unifiedAdminCount = _count.id;
          break;
        case 'mentor':
          unifiedMentorCount = _count.id;
          break;
        case 'super_admin':
          unifiedSuperAdminCount = _count.id;
          break;
      }
    });
    
    // Count records in old tables
    const oldTableCounts = await Promise.all([
      prisma.user.count(),
      prisma.admin.count(),
      prisma.mentor.count(),
    ]);
    
    const [userCount, adminCount, mentorCount] = oldTableCounts;
    
    // Validate counts (excluding super admins as they don't exist in old schema)
    const userCountMatch = unifiedUserCount === userCount;
    const adminCountMatch = unifiedAdminCount === adminCount;
    const mentorCountMatch = unifiedMentorCount === mentorCount;
    
    console.log('📊 Rollback validation:');
    console.log(`  Users: ${unifiedUserCount} → ${userCount} ${userCountMatch ? '✅' : '❌'}`);
    console.log(`  Admins: ${unifiedAdminCount} → ${adminCount} ${adminCountMatch ? '✅' : '❌'}`);
    console.log(`  Mentors: ${unifiedMentorCount} → ${mentorCount} ${mentorCountMatch ? '✅' : '❌'}`);
    
    if (unifiedSuperAdminCount > 0) {
      console.log(`  Super Admins: ${unifiedSuperAdminCount} (remaining in unified table)`);
    }
    
    const allCountsMatch = userCountMatch && adminCountMatch && mentorCountMatch;
    
    if (!allCountsMatch) {
      console.error('❌ Rollback validation failed: Record counts do not match');
      return false;
    }
    
    // Validate data integrity in old tables
    const dataIntegrityChecks = await Promise.all([
      validateUserDataIntegrity(),
      validateAdminDataIntegrity(),
      validateMentorDataIntegrity(),
    ]);
    
    const allDataValid = dataIntegrityChecks.every(check => check);
    
    if (!allDataValid) {
      console.error('❌ Rollback validation failed: Data integrity issues found');
      return false;
    }
    
    console.log('✅ Rollback validation passed!');
    return true;
  } catch (error) {
    console.error('❌ Rollback validation error:', error);
    return false;
  }
}

async function validateUserDataIntegrity(): Promise<boolean> {
  try {
    // Check for required fields
    const usersWithMissingFields = await prisma.user.count({
      where: {
        OR: [
          { name: null },
          { email: null },
          { provider: null },
        ],
      },
    });
    
    if (usersWithMissingFields > 0) {
      console.error(`❌ Found ${usersWithMissingFields} users with missing required fields`);
      return false;
    }
    
    // Check email uniqueness
    const totalUsers = await prisma.user.count();
    const uniqueEmails = await prisma.user.count({
      distinct: ['email'],
    });
    
    if (totalUsers !== uniqueEmails) {
      console.error(`❌ User email uniqueness violation: ${totalUsers - uniqueEmails} duplicates`);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('❌ User data integrity validation failed:', error);
    return false;
  }
}

async function validateAdminDataIntegrity(): Promise<boolean> {
  try {
    // Check for required fields
    const adminsWithMissingFields = await prisma.admin.count({
      where: {
        OR: [
          { name: null },
          { email: null },
        ],
      },
    });
    
    if (adminsWithMissingFields > 0) {
      console.error(`❌ Found ${adminsWithMissingFields} admins with missing required fields`);
      return false;
    }
    
    // Check email uniqueness
    const totalAdmins = await prisma.admin.count();
    const uniqueEmails = await prisma.admin.count({
      distinct: ['email'],
    });
    
    if (totalAdmins !== uniqueEmails) {
      console.error(`❌ Admin email uniqueness violation: ${totalAdmins - uniqueEmails} duplicates`);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('❌ Admin data integrity validation failed:', error);
    return false;
  }
}

async function validateMentorDataIntegrity(): Promise<boolean> {
  try {
    // Check for required fields
    const mentorsWithMissingFields = await prisma.mentor.count({
      where: {
        OR: [
          { name: null },
          { email: null },
          { designation: null },
          { desc: null },
        ],
      },
    });
    
    if (mentorsWithMissingFields > 0) {
      console.error(`❌ Found ${mentorsWithMissingFields} mentors with missing required fields`);
      return false;
    }
    
    // Check email uniqueness
    const totalMentors = await prisma.mentor.count();
    const uniqueEmails = await prisma.mentor.count({
      distinct: ['email'],
    });
    
    if (totalMentors !== uniqueEmails) {
      console.error(`❌ Mentor email uniqueness violation: ${totalMentors - uniqueEmails} duplicates`);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('❌ Mentor data integrity validation failed:', error);
    return false;
  }
}

// Main execution
if (require.main === module) {
  // Add confirmation prompt
  console.log('⚠️  WARNING: This will rollback the user role migration!');
  console.log('⚠️  This action will restore data from the unified user table back to the original 3 tables.');
  console.log('⚠️  Super admin users will remain in the unified table as they have no equivalent in the old schema.');
  console.log('⚠️  Make sure you have a backup before proceeding!');
  
  // In a real scenario, you'd want to add a confirmation prompt here
  // For now, we'll proceed directly
  
  rollbackUserRoleMigration()
    .then(async (stats) => {
      const isValid = await validateRollback();
      if (isValid && stats.success && stats.errors.length === 0) {
        console.log('🎉 Rollback completed successfully with validation!');
        process.exit(0);
      } else {
        console.error('💥 Rollback completed but validation failed or errors occurred!');
        if (stats.errors.length > 0) {
          console.error('Errors encountered:');
          stats.errors.forEach(error => console.error(`  - ${error}`));
        }
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('💥 Rollback failed:', error);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { rollbackUserRoleMigration, validateRollback };
