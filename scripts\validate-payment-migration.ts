import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface ValidationReport {
  totalOldRecords: number;
  totalNewRecords: number;
  recordCountMatch: boolean;
  dataIntegrityChecks: {
    userMentorServices: boolean;
    guestMentorServices: boolean;
    userPackages: boolean;
    guestPackages: boolean;
    userImmigrationServices: boolean;
    guestImmigrationServices: boolean;
    userTraining: boolean;
    guestTraining: boolean;
  };
  foreignKeyIntegrity: boolean;
  constraintValidation: boolean;
  overallValid: boolean;
}

async function validatePaymentMigration(): Promise<ValidationReport> {
  console.log('🔍 Starting comprehensive payment migration validation...');
  
  const report: ValidationReport = {
    totalOldRecords: 0,
    totalNewRecords: 0,
    recordCountMatch: false,
    dataIntegrityChecks: {
      userMentorServices: false,
      guestMentorServices: false,
      userPackages: false,
      guestPackages: false,
      userImmigrationServices: false,
      guestImmigrationServices: false,
      userTraining: false,
      guestTraining: false,
    },
    foreignKeyIntegrity: false,
    constraintValidation: false,
    overallValid: false,
  };

  try {
    // 1. Count validation
    await validateRecordCounts(report);
    
    // 2. Data integrity validation
    await validateDataIntegrity(report);
    
    // 3. Foreign key integrity validation
    await validateForeignKeyIntegrity(report);
    
    // 4. Constraint validation
    await validateConstraints(report);
    
    // 5. Overall validation
    report.overallValid = 
      report.recordCountMatch &&
      Object.values(report.dataIntegrityChecks).every(check => check) &&
      report.foreignKeyIntegrity &&
      report.constraintValidation;
    
    if (report.overallValid) {
      console.log('✅ All validation checks passed!');
    } else {
      console.error('❌ Some validation checks failed!');
    }
    
    return report;
  } catch (error) {
    console.error('💥 Validation failed with error:', error);
    throw error;
  }
}

async function validateRecordCounts(report: ValidationReport): Promise<void> {
  console.log('📊 Validating record counts...');
  
  const oldTableCounts = await Promise.all([
    prisma.user_mentor_service.count(),
    prisma.guest_mentor_service.count(),
    prisma.user_package.count(),
    prisma.guest_package.count(),
    prisma.user_immigration_service.count(),
    prisma.guest_immigration_service.count(),
    prisma.user_training.count(),
    prisma.guest_training.count(),
  ]);
  
  report.totalOldRecords = oldTableCounts.reduce((sum, count) => sum + count, 0);
  report.totalNewRecords = await prisma.payment.count();
  report.recordCountMatch = report.totalOldRecords === report.totalNewRecords;
  
  console.log(`📈 Old tables total: ${report.totalOldRecords}`);
  console.log(`📈 New table total: ${report.totalNewRecords}`);
  console.log(`${report.recordCountMatch ? '✅' : '❌'} Record count match: ${report.recordCountMatch}`);
}

async function validateDataIntegrity(report: ValidationReport): Promise<void> {
  console.log('🔍 Validating data integrity...');
  
  // Validate user mentor services
  report.dataIntegrityChecks.userMentorServices = await validateUserMentorServices();
  
  // Validate guest mentor services
  report.dataIntegrityChecks.guestMentorServices = await validateGuestMentorServices();
  
  // Validate user packages
  report.dataIntegrityChecks.userPackages = await validateUserPackages();
  
  // Validate guest packages
  report.dataIntegrityChecks.guestPackages = await validateGuestPackages();
  
  // Validate user immigration services
  report.dataIntegrityChecks.userImmigrationServices = await validateUserImmigrationServices();
  
  // Validate guest immigration services
  report.dataIntegrityChecks.guestImmigrationServices = await validateGuestImmigrationServices();
  
  // Validate user training
  report.dataIntegrityChecks.userTraining = await validateUserTraining();
  
  // Validate guest training
  report.dataIntegrityChecks.guestTraining = await validateGuestTraining();
}

async function validateUserMentorServices(): Promise<boolean> {
  const oldRecords = await prisma.user_mentor_service.findMany();
  const newRecords = await prisma.payment.findMany({
    where: {
      payment_type: 'user',
      service_type: 'mentor',
    },
  });
  
  if (oldRecords.length !== newRecords.length) {
    console.error(`❌ User mentor services count mismatch: ${oldRecords.length} vs ${newRecords.length}`);
    return false;
  }
  
  for (const oldRecord of oldRecords) {
    const newRecord = newRecords.find(r => r.id === oldRecord.id);
    if (!newRecord) {
      console.error(`❌ Missing user mentor service record: ${oldRecord.id}`);
      return false;
    }
    
    if (
      newRecord.amount !== oldRecord.amount ||
      newRecord.status !== oldRecord.status ||
      newRecord.userId !== oldRecord.userId ||
      newRecord.serviceId !== oldRecord.serviceId
    ) {
      console.error(`❌ Data mismatch for user mentor service: ${oldRecord.id}`);
      return false;
    }
  }
  
  console.log(`✅ User mentor services validation passed (${oldRecords.length} records)`);
  return true;
}

async function validateGuestMentorServices(): Promise<boolean> {
  const oldRecords = await prisma.guest_mentor_service.findMany();
  const newRecords = await prisma.payment.findMany({
    where: {
      payment_type: 'guest',
      service_type: 'mentor',
    },
  });
  
  if (oldRecords.length !== newRecords.length) {
    console.error(`❌ Guest mentor services count mismatch: ${oldRecords.length} vs ${newRecords.length}`);
    return false;
  }
  
  for (const oldRecord of oldRecords) {
    const newRecord = newRecords.find(r => r.id === oldRecord.id);
    if (!newRecord) {
      console.error(`❌ Missing guest mentor service record: ${oldRecord.id}`);
      return false;
    }
    
    if (
      newRecord.amount !== oldRecord.amount ||
      newRecord.status !== oldRecord.status ||
      newRecord.serviceId !== oldRecord.serviceId ||
      newRecord.guest_name !== oldRecord.name ||
      newRecord.guest_email !== oldRecord.email ||
      newRecord.guest_mobile !== oldRecord.mobile_no
    ) {
      console.error(`❌ Data mismatch for guest mentor service: ${oldRecord.id}`);
      return false;
    }
  }
  
  console.log(`✅ Guest mentor services validation passed (${oldRecords.length} records)`);
  return true;
}

async function validateUserPackages(): Promise<boolean> {
  const oldRecords = await prisma.user_package.findMany();
  const newRecords = await prisma.payment.findMany({
    where: {
      payment_type: 'user',
      service_type: 'package',
    },
  });
  
  if (oldRecords.length !== newRecords.length) {
    console.error(`❌ User packages count mismatch: ${oldRecords.length} vs ${newRecords.length}`);
    return false;
  }
  
  for (const oldRecord of oldRecords) {
    const newRecord = newRecords.find(r => r.id === oldRecord.id);
    if (!newRecord) {
      console.error(`❌ Missing user package record: ${oldRecord.id}`);
      return false;
    }
    
    if (
      newRecord.amount !== oldRecord.amount ||
      newRecord.status !== oldRecord.status ||
      newRecord.userId !== oldRecord.userId ||
      newRecord.packageId !== oldRecord.packageId
    ) {
      console.error(`❌ Data mismatch for user package: ${oldRecord.id}`);
      return false;
    }
  }
  
  console.log(`✅ User packages validation passed (${oldRecords.length} records)`);
  return true;
}

async function validateGuestPackages(): Promise<boolean> {
  const oldRecords = await prisma.guest_package.findMany();
  const newRecords = await prisma.payment.findMany({
    where: {
      payment_type: 'guest',
      service_type: 'package',
    },
  });
  
  if (oldRecords.length !== newRecords.length) {
    console.error(`❌ Guest packages count mismatch: ${oldRecords.length} vs ${newRecords.length}`);
    return false;
  }
  
  for (const oldRecord of oldRecords) {
    const newRecord = newRecords.find(r => r.id === oldRecord.id);
    if (!newRecord) {
      console.error(`❌ Missing guest package record: ${oldRecord.id}`);
      return false;
    }
    
    if (
      newRecord.amount !== oldRecord.amount ||
      newRecord.status !== oldRecord.status ||
      newRecord.packageId !== oldRecord.packageId ||
      newRecord.guest_name !== oldRecord.name ||
      newRecord.guest_email !== oldRecord.email ||
      newRecord.guest_mobile !== oldRecord.mobile_no
    ) {
      console.error(`❌ Data mismatch for guest package: ${oldRecord.id}`);
      return false;
    }
  }
  
  console.log(`✅ Guest packages validation passed (${oldRecords.length} records)`);
  return true;
}

async function validateUserImmigrationServices(): Promise<boolean> {
  const oldRecords = await prisma.user_immigration_service.findMany();
  const newRecords = await prisma.payment.findMany({
    where: {
      payment_type: 'user',
      service_type: 'immigration',
    },
  });
  
  if (oldRecords.length !== newRecords.length) {
    console.error(`❌ User immigration services count mismatch: ${oldRecords.length} vs ${newRecords.length}`);
    return false;
  }
  
  console.log(`✅ User immigration services validation passed (${oldRecords.length} records)`);
  return true;
}

async function validateGuestImmigrationServices(): Promise<boolean> {
  const oldRecords = await prisma.guest_immigration_service.findMany();
  const newRecords = await prisma.payment.findMany({
    where: {
      payment_type: 'guest',
      service_type: 'immigration',
    },
  });
  
  if (oldRecords.length !== newRecords.length) {
    console.error(`❌ Guest immigration services count mismatch: ${oldRecords.length} vs ${newRecords.length}`);
    return false;
  }
  
  console.log(`✅ Guest immigration services validation passed (${oldRecords.length} records)`);
  return true;
}

async function validateUserTraining(): Promise<boolean> {
  const oldRecords = await prisma.user_training.findMany();
  const newRecords = await prisma.payment.findMany({
    where: {
      payment_type: 'user',
      service_type: 'training',
    },
  });
  
  if (oldRecords.length !== newRecords.length) {
    console.error(`❌ User training count mismatch: ${oldRecords.length} vs ${newRecords.length}`);
    return false;
  }
  
  console.log(`✅ User training validation passed (${oldRecords.length} records)`);
  return true;
}

async function validateGuestTraining(): Promise<boolean> {
  const oldRecords = await prisma.guest_training.findMany();
  const newRecords = await prisma.payment.findMany({
    where: {
      payment_type: 'guest',
      service_type: 'training',
    },
  });
  
  if (oldRecords.length !== newRecords.length) {
    console.error(`❌ Guest training count mismatch: ${oldRecords.length} vs ${newRecords.length}`);
    return false;
  }
  
  console.log(`✅ Guest training validation passed (${oldRecords.length} records)`);
  return true;
}

async function validateForeignKeyIntegrity(report: ValidationReport): Promise<void> {
  console.log('🔗 Validating foreign key integrity...');
  
  try {
    // Check user references
    const userPayments = await prisma.payment.findMany({
      where: { payment_type: 'user' },
      include: { user: true },
    });
    
    const invalidUserRefs = userPayments.filter(p => p.userId && !p.user);
    if (invalidUserRefs.length > 0) {
      console.error(`❌ Found ${invalidUserRefs.length} payments with invalid user references`);
      report.foreignKeyIntegrity = false;
      return;
    }
    
    // Check service references
    const mentorPayments = await prisma.payment.findMany({
      where: { service_type: 'mentor' },
      include: { service: true },
    });
    
    const invalidServiceRefs = mentorPayments.filter(p => p.serviceId && !p.service);
    if (invalidServiceRefs.length > 0) {
      console.error(`❌ Found ${invalidServiceRefs.length} payments with invalid service references`);
      report.foreignKeyIntegrity = false;
      return;
    }
    
    report.foreignKeyIntegrity = true;
    console.log('✅ Foreign key integrity validation passed');
  } catch (error) {
    console.error('❌ Foreign key integrity validation failed:', error);
    report.foreignKeyIntegrity = false;
  }
}

async function validateConstraints(report: ValidationReport): Promise<void> {
  console.log('⚖️ Validating constraints...');
  
  try {
    // Check payment_type constraints
    const invalidPaymentTypes = await prisma.payment.findMany({
      where: {
        payment_type: {
          notIn: ['user', 'guest'],
        },
      },
    });
    
    if (invalidPaymentTypes.length > 0) {
      console.error(`❌ Found ${invalidPaymentTypes.length} payments with invalid payment_type`);
      report.constraintValidation = false;
      return;
    }
    
    // Check service_type constraints
    const invalidServiceTypes = await prisma.payment.findMany({
      where: {
        service_type: {
          notIn: ['mentor', 'package', 'immigration', 'training'],
        },
      },
    });
    
    if (invalidServiceTypes.length > 0) {
      console.error(`❌ Found ${invalidServiceTypes.length} payments with invalid service_type`);
      report.constraintValidation = false;
      return;
    }
    
    report.constraintValidation = true;
    console.log('✅ Constraint validation passed');
  } catch (error) {
    console.error('❌ Constraint validation failed:', error);
    report.constraintValidation = false;
  }
}

// Main execution
if (require.main === module) {
  validatePaymentMigration()
    .then((report) => {
      console.log('\n📋 Validation Report:');
      console.log(JSON.stringify(report, null, 2));
      
      if (report.overallValid) {
        console.log('\n🎉 Migration validation completed successfully!');
        process.exit(0);
      } else {
        console.error('\n💥 Migration validation failed!');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('💥 Validation failed:', error);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { validatePaymentMigration };
