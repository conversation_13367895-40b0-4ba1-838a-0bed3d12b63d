import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface ValidationReport {
  totalOldRecords: number;
  totalNewRecords: number;
  recordCountMatch: boolean;
  roleDistribution: {
    user: number;
    admin: number;
    mentor: number;
    super_admin: number;
  };
  dataIntegrityChecks: {
    emailUniqueness: boolean;
    mentorFieldsValid: boolean;
    permissionsValid: boolean;
    statusValid: boolean;
    providerValid: boolean;
  };
  foreignKeyIntegrity: boolean;
  constraintValidation: boolean;
  migrationErrors: string[];
  overallValid: boolean;
}

async function validateUserRoleMigration(): Promise<ValidationReport> {
  console.log('🔍 Starting comprehensive user role migration validation...');
  
  const report: ValidationReport = {
    totalOldRecords: 0,
    totalNewRecords: 0,
    recordCountMatch: false,
    roleDistribution: {
      user: 0,
      admin: 0,
      mentor: 0,
      super_admin: 0,
    },
    dataIntegrityChecks: {
      emailUniqueness: false,
      mentorFieldsValid: false,
      permissionsValid: false,
      statusValid: false,
      providerValid: false,
    },
    foreignKeyIntegrity: false,
    constraintValidation: false,
    migrationErrors: [],
    overallValid: false,
  };

  try {
    // 1. Count validation
    await validateRecordCounts(report);
    
    // 2. Role distribution validation
    await validateRoleDistribution(report);
    
    // 3. Data integrity validation
    await validateDataIntegrity(report);
    
    // 4. Foreign key integrity validation
    await validateForeignKeyIntegrity(report);
    
    // 5. Constraint validation
    await validateConstraints(report);
    
    // 6. Overall validation
    report.overallValid = 
      report.recordCountMatch &&
      Object.values(report.dataIntegrityChecks).every(check => check) &&
      report.foreignKeyIntegrity &&
      report.constraintValidation &&
      report.migrationErrors.length === 0;
    
    if (report.overallValid) {
      console.log('✅ All validation checks passed!');
    } else {
      console.error('❌ Some validation checks failed!');
    }
    
    return report;
  } catch (error) {
    console.error('💥 Validation failed with error:', error);
    report.migrationErrors.push(`Validation error: ${error.message}`);
    throw error;
  }
}

async function validateRecordCounts(report: ValidationReport): Promise<void> {
  console.log('📊 Validating record counts...');
  
  const oldTableCounts = await Promise.all([
    prisma.user.count(),
    prisma.admin.count(),
    prisma.mentor.count(),
  ]);
  
  report.totalOldRecords = oldTableCounts.reduce((sum, count) => sum + count, 0);
  report.totalNewRecords = await prisma.unified_user.count();
  report.recordCountMatch = report.totalOldRecords === report.totalNewRecords;
  
  console.log(`📈 Old tables total: ${report.totalOldRecords}`);
  console.log(`📈 New table total: ${report.totalNewRecords}`);
  console.log(`${report.recordCountMatch ? '✅' : '❌'} Record count match: ${report.recordCountMatch}`);
  
  if (!report.recordCountMatch) {
    report.migrationErrors.push(`Record count mismatch: ${report.totalOldRecords} old vs ${report.totalNewRecords} new`);
  }
}

async function validateRoleDistribution(report: ValidationReport): Promise<void> {
  console.log('👥 Validating role distribution...');
  
  const roleCounts = await prisma.unified_user.groupBy({
    by: ['role'],
    _count: { id: true },
  });
  
  roleCounts.forEach(({ role, _count }) => {
    switch (role) {
      case 'user':
        report.roleDistribution.user = _count.id;
        break;
      case 'admin':
        report.roleDistribution.admin = _count.id;
        break;
      case 'mentor':
        report.roleDistribution.mentor = _count.id;
        break;
      case 'super_admin':
        report.roleDistribution.super_admin = _count.id;
        break;
    }
  });
  
  // Validate against original table counts
  const originalUserCount = await prisma.user.count();
  const originalAdminCount = await prisma.admin.count();
  const originalMentorCount = await prisma.mentor.count();
  
  const userCountMatch = report.roleDistribution.user === originalUserCount;
  const adminCountMatch = report.roleDistribution.admin === originalAdminCount;
  const mentorCountMatch = report.roleDistribution.mentor === originalMentorCount;
  
  console.log('📊 Role distribution:');
  console.log(`  Users: ${report.roleDistribution.user} ${userCountMatch ? '✅' : '❌'}`);
  console.log(`  Admins: ${report.roleDistribution.admin} ${adminCountMatch ? '✅' : '❌'}`);
  console.log(`  Mentors: ${report.roleDistribution.mentor} ${mentorCountMatch ? '✅' : '❌'}`);
  console.log(`  Super Admins: ${report.roleDistribution.super_admin}`);
  
  if (!userCountMatch || !adminCountMatch || !mentorCountMatch) {
    report.migrationErrors.push('Role distribution does not match original table counts');
  }
}

async function validateDataIntegrity(report: ValidationReport): Promise<void> {
  console.log('🔍 Validating data integrity...');
  
  // 1. Email uniqueness validation
  const totalUsers = await prisma.unified_user.count();
  const uniqueEmails = await prisma.unified_user.count({
    distinct: ['email'],
  });
  
  report.dataIntegrityChecks.emailUniqueness = totalUsers === uniqueEmails;
  console.log(`${report.dataIntegrityChecks.emailUniqueness ? '✅' : '❌'} Email uniqueness: ${uniqueEmails}/${totalUsers} unique`);
  
  if (!report.dataIntegrityChecks.emailUniqueness) {
    report.migrationErrors.push(`Email uniqueness violation: ${totalUsers - uniqueEmails} duplicate emails`);
  }
  
  // 2. Mentor fields validation
  const mentorUsers = await prisma.unified_user.findMany({
    where: { role: 'mentor' },
    select: { id: true, designation: true, description: true, email: true },
  });
  
  const invalidMentors = mentorUsers.filter(
    mentor => !mentor.designation || !mentor.description
  );
  
  report.dataIntegrityChecks.mentorFieldsValid = invalidMentors.length === 0;
  console.log(`${report.dataIntegrityChecks.mentorFieldsValid ? '✅' : '❌'} Mentor fields validation: ${invalidMentors.length} invalid mentors`);
  
  if (!report.dataIntegrityChecks.mentorFieldsValid) {
    report.migrationErrors.push(`Invalid mentor fields: ${invalidMentors.length} mentors missing required fields`);
    invalidMentors.forEach(mentor => {
      console.error(`  - ${mentor.email}: missing ${!mentor.designation ? 'designation' : ''} ${!mentor.description ? 'description' : ''}`);
    });
  }
  
  // 3. Permissions validation
  const usersWithoutPermissions = await prisma.unified_user.count({
    where: {
      OR: [
        { permissions: { equals: null } },
        { permissions: { equals: [] } },
      ],
    },
  });
  
  report.dataIntegrityChecks.permissionsValid = usersWithoutPermissions === 0;
  console.log(`${report.dataIntegrityChecks.permissionsValid ? '✅' : '❌'} Permissions validation: ${usersWithoutPermissions} users without permissions`);
  
  if (!report.dataIntegrityChecks.permissionsValid) {
    report.migrationErrors.push(`Permissions validation failed: ${usersWithoutPermissions} users without permissions`);
  }
  
  // 4. Status validation
  const validStatuses = ['active', 'inactive', 'pending', 'suspended', 'deleted'];
  const invalidStatusUsers = await prisma.unified_user.count({
    where: {
      status: {
        notIn: validStatuses,
      },
    },
  });
  
  report.dataIntegrityChecks.statusValid = invalidStatusUsers === 0;
  console.log(`${report.dataIntegrityChecks.statusValid ? '✅' : '❌'} Status validation: ${invalidStatusUsers} users with invalid status`);
  
  if (!report.dataIntegrityChecks.statusValid) {
    report.migrationErrors.push(`Status validation failed: ${invalidStatusUsers} users with invalid status`);
  }
  
  // 5. Provider validation
  const validProviders = ['credentials', 'google', 'facebook', 'linkedin'];
  const invalidProviderUsers = await prisma.unified_user.count({
    where: {
      provider: {
        notIn: validProviders,
      },
    },
  });
  
  report.dataIntegrityChecks.providerValid = invalidProviderUsers === 0;
  console.log(`${report.dataIntegrityChecks.providerValid ? '✅' : '❌'} Provider validation: ${invalidProviderUsers} users with invalid provider`);
  
  if (!report.dataIntegrityChecks.providerValid) {
    report.migrationErrors.push(`Provider validation failed: ${invalidProviderUsers} users with invalid provider`);
  }
}

async function validateForeignKeyIntegrity(report: ValidationReport): Promise<void> {
  console.log('🔗 Validating foreign key integrity...');
  
  try {
    // Check review table references
    const reviewsWithInvalidUsers = await prisma.$queryRaw`
      SELECT COUNT(*) as count
      FROM review r
      LEFT JOIN unified_user u ON r."userId" = u.id
      WHERE r."userId" IS NOT NULL AND u.id IS NULL
    `;
    
    const reviewsWithInvalidMentors = await prisma.$queryRaw`
      SELECT COUNT(*) as count
      FROM review r
      LEFT JOIN unified_user u ON r."mentorId" = u.id
      WHERE r."mentorId" IS NOT NULL AND u.id IS NULL
    `;
    
    // Check service table references
    const servicesWithInvalidMentors = await prisma.$queryRaw`
      SELECT COUNT(*) as count
      FROM service s
      LEFT JOIN unified_user u ON s."mentorId" = u.id
      WHERE s."mentorId" IS NOT NULL AND u.id IS NULL
    `;
    
    // Check comment table references
    const commentsWithInvalidAuthors = await prisma.$queryRaw`
      SELECT COUNT(*) as count
      FROM comment c
      LEFT JOIN unified_user u ON c."authorId" = u.id
      WHERE c."authorId" IS NOT NULL AND u.id IS NULL
    `;
    
    const totalInvalidReferences = 
      Number((reviewsWithInvalidUsers as any)[0]?.count || 0) +
      Number((reviewsWithInvalidMentors as any)[0]?.count || 0) +
      Number((servicesWithInvalidMentors as any)[0]?.count || 0) +
      Number((commentsWithInvalidAuthors as any)[0]?.count || 0);
    
    report.foreignKeyIntegrity = totalInvalidReferences === 0;
    
    console.log(`${report.foreignKeyIntegrity ? '✅' : '❌'} Foreign key integrity: ${totalInvalidReferences} invalid references`);
    
    if (!report.foreignKeyIntegrity) {
      report.migrationErrors.push(`Foreign key integrity failed: ${totalInvalidReferences} invalid references found`);
    }
  } catch (error) {
    console.error('❌ Foreign key integrity validation failed:', error);
    report.foreignKeyIntegrity = false;
    report.migrationErrors.push(`Foreign key validation error: ${error.message}`);
  }
}

async function validateConstraints(report: ValidationReport): Promise<void> {
  console.log('⚖️ Validating constraints...');
  
  try {
    // Check role constraints
    const invalidRoles = await prisma.unified_user.count({
      where: {
        role: {
          notIn: ['user', 'admin', 'mentor', 'super_admin'],
        },
      },
    });
    
    // Check status constraints
    const invalidStatuses = await prisma.unified_user.count({
      where: {
        status: {
          notIn: ['active', 'inactive', 'pending', 'suspended', 'deleted'],
        },
      },
    });
    
    // Check provider constraints
    const invalidProviders = await prisma.unified_user.count({
      where: {
        provider: {
          notIn: ['credentials', 'google', 'facebook', 'linkedin'],
        },
      },
    });
    
    const totalConstraintViolations = invalidRoles + invalidStatuses + invalidProviders;
    
    report.constraintValidation = totalConstraintViolations === 0;
    
    console.log(`${report.constraintValidation ? '✅' : '❌'} Constraint validation: ${totalConstraintViolations} violations`);
    
    if (invalidRoles > 0) {
      console.error(`  - Invalid roles: ${invalidRoles}`);
    }
    if (invalidStatuses > 0) {
      console.error(`  - Invalid statuses: ${invalidStatuses}`);
    }
    if (invalidProviders > 0) {
      console.error(`  - Invalid providers: ${invalidProviders}`);
    }
    
    if (!report.constraintValidation) {
      report.migrationErrors.push(`Constraint validation failed: ${totalConstraintViolations} violations`);
    }
  } catch (error) {
    console.error('❌ Constraint validation failed:', error);
    report.constraintValidation = false;
    report.migrationErrors.push(`Constraint validation error: ${error.message}`);
  }
}

async function generateMigrationReport(report: ValidationReport): Promise<void> {
  console.log('\n📋 Migration Validation Report:');
  console.log('=====================================');
  
  console.log('\n📊 Record Counts:');
  console.log(`  Old tables: ${report.totalOldRecords}`);
  console.log(`  New table: ${report.totalNewRecords}`);
  console.log(`  Match: ${report.recordCountMatch ? '✅' : '❌'}`);
  
  console.log('\n👥 Role Distribution:');
  console.log(`  Users: ${report.roleDistribution.user}`);
  console.log(`  Admins: ${report.roleDistribution.admin}`);
  console.log(`  Mentors: ${report.roleDistribution.mentor}`);
  console.log(`  Super Admins: ${report.roleDistribution.super_admin}`);
  
  console.log('\n🔍 Data Integrity:');
  console.log(`  Email uniqueness: ${report.dataIntegrityChecks.emailUniqueness ? '✅' : '❌'}`);
  console.log(`  Mentor fields: ${report.dataIntegrityChecks.mentorFieldsValid ? '✅' : '❌'}`);
  console.log(`  Permissions: ${report.dataIntegrityChecks.permissionsValid ? '✅' : '❌'}`);
  console.log(`  Status values: ${report.dataIntegrityChecks.statusValid ? '✅' : '❌'}`);
  console.log(`  Provider values: ${report.dataIntegrityChecks.providerValid ? '✅' : '❌'}`);
  
  console.log('\n🔗 System Integrity:');
  console.log(`  Foreign keys: ${report.foreignKeyIntegrity ? '✅' : '❌'}`);
  console.log(`  Constraints: ${report.constraintValidation ? '✅' : '❌'}`);
  
  if (report.migrationErrors.length > 0) {
    console.log('\n❌ Errors Found:');
    report.migrationErrors.forEach((error, index) => {
      console.log(`  ${index + 1}. ${error}`);
    });
  }
  
  console.log(`\n🎯 Overall Status: ${report.overallValid ? '✅ PASSED' : '❌ FAILED'}`);
  console.log('=====================================\n');
}

// Main execution
if (require.main === module) {
  validateUserRoleMigration()
    .then(async (report) => {
      await generateMigrationReport(report);
      
      if (report.overallValid) {
        console.log('🎉 User role migration validation completed successfully!');
        process.exit(0);
      } else {
        console.error('💥 User role migration validation failed!');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('💥 Validation failed:', error);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { validateUserRoleMigration, generateMigrationReport };
