import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { CommentService } from './comment.service';
import { JwtGuard } from 'src/guards/jwt.guard';
import { GetUser } from 'src/decorator/user.decorator';
import { CommentDto } from './dto/comment.dto';
import { JwtAdmin } from 'src/guards/jwt.admin.guard';

@ApiTags('comment')
@Controller('comment')
export class CommentController {
  constructor(private comment: CommentService) {}

  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @Post()
  @ApiOperation({
    summary: '(User only)',
    description:
      'This API is restricted to  users and requires a Bearer token for authentication.',
  })
  async create(@GetUser() user: IJWTPayload, @Body() dto: CommentDto) {
    return await this.comment.comment(user, dto);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Delete(':id')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin  users and requires a Bearer token for authentication.',
  })
  async remove(@Param('id') id: string) {
    return await this.comment.remove(id);
  }

  @Get(':blogId')
  async getBlogs(
    @Param('blogId') blogId: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    return await this.comment.getComments(blogId, page, limit);
  }
}
