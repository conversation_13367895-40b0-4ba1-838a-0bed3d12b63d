import { Modu<PERSON> } from '@nestjs/common';
import { DashboardController } from './dashboard.controller';
import { PrismaService } from 'src/utils/prisma.service';
import { JwtService } from '@nestjs/jwt';
import { DashboardService } from './dashboard.service';

@Module({
  controllers: [DashboardController],
  providers: [PrismaService, JwtService, DashboardService],
})
export class DashboardModule {}
