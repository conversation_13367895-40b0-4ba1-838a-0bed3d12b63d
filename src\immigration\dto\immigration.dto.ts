import { ApiProperty } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON>y, IsInt, IsString, Min, IsNotEmpty, IsOptional } from 'class-validator';

export class ImmigrationDto {
  @ApiProperty({ description: 'Name of the package' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'Amount of the package in integer format' })
  @IsInt()
  @Min(0)
  amount: number;

  @ApiProperty({ description: 'Training order' })
  @IsOptional()
  @IsInt()
  @Min(0)
  order: number;

  @ApiProperty({
    description: 'List of services included in the package',
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  service: string[];
}
