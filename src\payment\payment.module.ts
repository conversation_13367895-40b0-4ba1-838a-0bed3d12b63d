import { Modu<PERSON> } from '@nestjs/common';
import { PaymentController } from './payment.controller';
import { PaymentService } from './payment.service';
import { StripeProvider } from 'src/config/stripe.config';
import { PrismaService } from 'src/utils/prisma.service';
import { JwtService } from '@nestjs/jwt';
import { MailerService } from 'src/mailer/mailer.service';

@Module({
  controllers: [PaymentController],
  providers: [
    PaymentService,
    StripeProvider,
    PrismaService,
    JwtService,
    MailerService,
  ],
})
export class PaymentModule {}
