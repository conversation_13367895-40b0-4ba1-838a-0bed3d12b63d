import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Param,
  UseGuards,
  DefaultValuePipe,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags, ApiQuery } from '@nestjs/swagger';
import { UnifiedPaymentService } from './unified-payment.service';
import { JwtGuard } from 'src/guards/jwt.guard';
import { JwtAdmin } from 'src/guards/jwt.admin.guard';
import { GetUser } from 'src/decorator/user.decorator';
import {
  UserMentorServiceDto,
  UserPackageServiceDto,
  UserImmigrationServiceDto,
  UserTrainingServiceDto,
} from './dto/payment.dto';

@ApiTags('payment-v2')
@Controller('v2/payment')
export class UnifiedPaymentController {
  constructor(private unifiedPaymentService: UnifiedPaymentService) {}

  /**
   * Create a unified payment for any service type
   */
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @Post('/create')
  @ApiOperation({
    summary: 'Create unified payment (User only)',
    description: 'Create a payment for any service type using unified structure',
  })
  async createPayment(
    @GetUser() user: IJWTPayload,
    @Body() dto: any, // You would create a unified DTO
  ) {
    const paymentData = {
      amount: dto.amount,
      status: 'pending',
      payment_type: 'user' as const,
      service_type: dto.service_type,
      userId: user.id,
      serviceId: dto.serviceId,
      packageId: dto.packageId,
      immigration_serviceId: dto.immigration_serviceId,
      trainingId: dto.trainingId,
    };

    return await this.unifiedPaymentService.createPayment(paymentData);
  }

  /**
   * Create a guest payment for any service type
   */
  @Post('/guest/create')
  @ApiOperation({
    summary: 'Create unified guest payment',
    description: 'Create a guest payment for any service type using unified structure',
  })
  async createGuestPayment(@Body() dto: any) {
    const paymentData = {
      amount: dto.amount,
      status: 'pending',
      payment_type: 'guest' as const,
      service_type: dto.service_type,
      guest_name: dto.name,
      guest_email: dto.email,
      guest_mobile: dto.mobile_no,
      serviceId: dto.serviceId,
      packageId: dto.packageId,
      immigration_serviceId: dto.immigration_serviceId,
      trainingId: dto.trainingId,
    };

    return await this.unifiedPaymentService.createPayment(paymentData);
  }

  /**
   * Get all payments with filtering
   */
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Get('/admin/all')
  @ApiOperation({
    summary: 'Get all payments (Admin only)',
    description: 'Get all payments with filtering and pagination',
  })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'payment_type', required: false, enum: ['user', 'guest'] })
  @ApiQuery({ name: 'service_type', required: false, enum: ['mentor', 'package', 'immigration', 'training'] })
  @ApiQuery({ name: 'status', required: false, type: String })
  async getAllPayments(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('payment_type') payment_type?: 'user' | 'guest',
    @Query('service_type') service_type?: 'mentor' | 'package' | 'immigration' | 'training',
    @Query('status') status?: string,
  ) {
    return await this.unifiedPaymentService.getPayments({
      page,
      limit,
      payment_type,
      service_type,
      status,
    });
  }

  /**
   * Get user's payment history
   */
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @Get('/user/history')
  @ApiOperation({
    summary: 'Get user payment history (User only)',
    description: 'Get authenticated user\'s payment history',
  })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'service_type', required: false, enum: ['mentor', 'package', 'immigration', 'training'] })
  async getUserPaymentHistory(
    @GetUser() user: IJWTPayload,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('service_type') service_type?: 'mentor' | 'package' | 'immigration' | 'training',
  ) {
    return await this.unifiedPaymentService.getPayments({
      page,
      limit,
      payment_type: 'user',
      service_type,
      userId: user.id,
    });
  }

  /**
   * Get guest payments by service type (Admin only)
   */
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Get('/admin/guest/:serviceType')
  @ApiOperation({
    summary: 'Get guest payments by service type (Admin only)',
    description: 'Get guest payments filtered by service type',
  })
  async getGuestPaymentsByType(
    @Param('serviceType') serviceType: 'mentor' | 'package' | 'immigration' | 'training',
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    return await this.unifiedPaymentService.getPayments({
      page,
      limit,
      payment_type: 'guest',
      service_type: serviceType,
    });
  }

  /**
   * Get payment by ID
   */
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Get('/admin/:id')
  @ApiOperation({
    summary: 'Get payment by ID (Admin only)',
    description: 'Get detailed payment information by ID',
  })
  async getPaymentById(@Param('id') id: string) {
    return await this.unifiedPaymentService.getPaymentById(id);
  }

  /**
   * Get revenue statistics
   */
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Get('/admin/stats/revenue')
  @ApiOperation({
    summary: 'Get revenue statistics (Admin only)',
    description: 'Get comprehensive revenue statistics across all service types',
  })
  async getRevenueStats() {
    return await this.unifiedPaymentService.getRevenueStats();
  }

  /**
   * Get user's total spending
   */
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @Get('/user/total-spent')
  @ApiOperation({
    summary: 'Get user total spending (User only)',
    description: 'Get total amount spent by the authenticated user',
  })
  async getUserTotalSpent(@GetUser() user: IJWTPayload) {
    const totalSpent = await this.unifiedPaymentService.getUserTotalSpent(user.id);
    return { total_spent: totalSpent.toString() };
  }

  /**
   * Get mentor revenue (Admin only)
   */
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Get('/admin/mentor/:mentorId/revenue')
  @ApiOperation({
    summary: 'Get mentor revenue (Admin only)',
    description: 'Get revenue statistics for a specific mentor',
  })
  async getMentorRevenue(@Param('mentorId') mentorId: string) {
    return await this.unifiedPaymentService.getMentorRevenue(mentorId);
  }

  // Legacy compatibility endpoints with transformation
  
  /**
   * Legacy mentor service endpoint (backward compatibility)
   */
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @Post('/mentor-service')
  @ApiOperation({
    summary: 'Create mentor service payment (Legacy compatibility)',
    description: 'Legacy endpoint for mentor service payments with backward compatible response',
  })
  async createMentorServiceLegacy(
    @GetUser() user: IJWTPayload,
    @Body() dto: UserMentorServiceDto,
  ) {
    const paymentData = {
      amount: 0, // Will be set from service price
      status: 'pending',
      payment_type: 'user' as const,
      service_type: 'mentor' as const,
      userId: user.id,
      serviceId: dto.serviceId,
    };

    const payment = await this.unifiedPaymentService.createPayment(paymentData);
    return this.unifiedPaymentService.transformToLegacyFormat(payment);
  }

  /**
   * Legacy guest service endpoint (backward compatibility)
   */
  @Post('/guest-service')
  @ApiOperation({
    summary: 'Create guest mentor service payment (Legacy compatibility)',
    description: 'Legacy endpoint for guest mentor service payments with backward compatible response',
  })
  async createGuestServiceLegacy(@Body() dto: UserMentorServiceDto) {
    const paymentData = {
      amount: 0, // Will be set from service price
      status: 'pending',
      payment_type: 'guest' as const,
      service_type: 'mentor' as const,
      guest_name: dto.name,
      guest_email: dto.email,
      guest_mobile: dto.mobile_no,
      serviceId: dto.serviceId,
    };

    const payment = await this.unifiedPaymentService.createPayment(paymentData);
    return this.unifiedPaymentService.transformToLegacyFormat(payment);
  }

  /**
   * Legacy package endpoint (backward compatibility)
   */
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @Post('/package')
  @ApiOperation({
    summary: 'Create package payment (Legacy compatibility)',
    description: 'Legacy endpoint for package payments with backward compatible response',
  })
  async createPackageLegacy(
    @GetUser() user: IJWTPayload,
    @Body() dto: UserPackageServiceDto,
  ) {
    const paymentData = {
      amount: 0, // Will be set from package price
      status: 'pending',
      payment_type: 'user' as const,
      service_type: 'package' as const,
      userId: user.id,
      packageId: dto.packageId,
    };

    const payment = await this.unifiedPaymentService.createPayment(paymentData);
    return this.unifiedPaymentService.transformToLegacyFormat(payment);
  }

  /**
   * Legacy guest package endpoint (backward compatibility)
   */
  @Post('/guest-package')
  @ApiOperation({
    summary: 'Create guest package payment (Legacy compatibility)',
    description: 'Legacy endpoint for guest package payments with backward compatible response',
  })
  async createGuestPackageLegacy(@Body() dto: UserPackageServiceDto) {
    const paymentData = {
      amount: 0, // Will be set from package price
      status: 'pending',
      payment_type: 'guest' as const,
      service_type: 'package' as const,
      guest_name: dto.name,
      guest_email: dto.email,
      guest_mobile: dto.mobile_no,
      packageId: dto.packageId,
    };

    const payment = await this.unifiedPaymentService.createPayment(paymentData);
    return this.unifiedPaymentService.transformToLegacyFormat(payment);
  }
}
